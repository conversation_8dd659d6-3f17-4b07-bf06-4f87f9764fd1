package com.vexleyofficial.warden.detection.data;

import org.bukkit.Location;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Stores player-specific data for detection analysis
 */
public class PlayerData {
    
    private final UUID playerId;
    private final String playerName;
    private final long creationTime;
    private final AtomicLong lastActivity;
    
    // Movement data
    private Location lastLocation;
    private Location currentLocation;
    private double lastVelocityX, lastVelocityY, lastVelocityZ;
    private boolean lastOnGround;
    private long lastMoveTime;
    
    // Combat data
    private long lastAttackTime;
    private int attackCount;
    private double lastReach;
    private final AtomicInteger cps = new AtomicInteger(0);
    private long lastClickTime;
    
    // Violation data
    private final Map<String, ViolationData> violations;
    
    // Rate limiting
    private final AtomicInteger checksThisSecond = new AtomicInteger(0);
    private long lastSecond;
    
    // Flags
    private boolean flying = false;
    private boolean sneaking = false;
    private boolean sprinting = false;
    private boolean inWater = false;
    private boolean inLava = false;
    private boolean inWeb = false;
    
    public PlayerData(@NotNull UUID playerId, @NotNull String playerName) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.creationTime = System.currentTimeMillis();
        this.lastActivity = new AtomicLong(System.currentTimeMillis());
        this.violations = new ConcurrentHashMap<>();
        this.lastSecond = System.currentTimeMillis() / 1000;
    }
    
    /**
     * Update last activity timestamp
     */
    public void updateActivity() {
        lastActivity.set(System.currentTimeMillis());
    }
    
    /**
     * Check if player can process another check (rate limiting)
     */
    public boolean canProcessCheck(int maxChecksPerSecond) {
        long currentSecond = System.currentTimeMillis() / 1000;
        
        if (currentSecond != lastSecond) {
            checksThisSecond.set(0);
            lastSecond = currentSecond;
        }
        
        return checksThisSecond.incrementAndGet() <= maxChecksPerSecond;
    }
    
    /**
     * Get or create violation data for a check type
     */
    @NotNull
    public ViolationData getViolationData(@NotNull String checkType) {
        return violations.computeIfAbsent(checkType, k -> new ViolationData(checkType));
    }
    
    /**
     * Add a violation
     */
    public void addViolation(@NotNull String checkType, double confidence) {
        ViolationData violationData = getViolationData(checkType);
        violationData.addViolation(confidence);
        updateActivity();
    }
    
    /**
     * Update movement data
     */
    public void updateMovement(@NotNull Location location, boolean onGround) {
        this.lastLocation = this.currentLocation;
        this.currentLocation = location.clone();
        this.lastOnGround = onGround;
        this.lastMoveTime = System.currentTimeMillis();
        updateActivity();
    }
    
    /**
     * Update velocity data
     */
    public void updateVelocity(double x, double y, double z) {
        this.lastVelocityX = x;
        this.lastVelocityY = y;
        this.lastVelocityZ = z;
        updateActivity();
    }
    
    /**
     * Update combat data
     */
    public void updateCombat(double reach) {
        this.lastAttackTime = System.currentTimeMillis();
        this.attackCount++;
        this.lastReach = reach;
        updateActivity();
    }
    
    /**
     * Update click data for CPS calculation
     */
    public void updateClick() {
        long currentTime = System.currentTimeMillis();
        this.lastClickTime = currentTime;
        
        // Simple CPS calculation (clicks in last second)
        // In a real implementation, you'd want a more sophisticated sliding window
        cps.incrementAndGet();
        updateActivity();
    }
    
    /**
     * Calculate movement speed
     */
    public double getMovementSpeed() {
        if (lastLocation == null || currentLocation == null) {
            return 0.0;
        }
        
        if (!lastLocation.getWorld().equals(currentLocation.getWorld())) {
            return 0.0;
        }
        
        double distance = lastLocation.distance(currentLocation);
        long timeDiff = System.currentTimeMillis() - lastMoveTime;
        
        if (timeDiff <= 0) {
            return 0.0;
        }
        
        return distance / (timeDiff / 1000.0); // blocks per second
    }
    
    /**
     * Calculate horizontal movement speed
     */
    public double getHorizontalSpeed() {
        if (lastLocation == null || currentLocation == null) {
            return 0.0;
        }
        
        if (!lastLocation.getWorld().equals(currentLocation.getWorld())) {
            return 0.0;
        }
        
        double deltaX = currentLocation.getX() - lastLocation.getX();
        double deltaZ = currentLocation.getZ() - lastLocation.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        long timeDiff = System.currentTimeMillis() - lastMoveTime;
        
        if (timeDiff <= 0) {
            return 0.0;
        }
        
        return distance / (timeDiff / 1000.0); // blocks per second
    }
    
    /**
     * Cleanup player data
     */
    public void cleanup() {
        violations.clear();
    }
    
    // Getters and setters
    public UUID getPlayerId() { return playerId; }
    public String getPlayerName() { return playerName; }
    public long getCreationTime() { return creationTime; }
    public long getLastActivity() { return lastActivity.get(); }
    
    @Nullable
    public Location getLastLocation() { return lastLocation; }
    @Nullable
    public Location getCurrentLocation() { return currentLocation; }
    
    public double getLastVelocityX() { return lastVelocityX; }
    public double getLastVelocityY() { return lastVelocityY; }
    public double getLastVelocityZ() { return lastVelocityZ; }
    
    public boolean isLastOnGround() { return lastOnGround; }
    public long getLastMoveTime() { return lastMoveTime; }
    
    public long getLastAttackTime() { return lastAttackTime; }
    public int getAttackCount() { return attackCount; }
    public double getLastReach() { return lastReach; }
    public int getCps() { return cps.get(); }
    public long getLastClickTime() { return lastClickTime; }
    
    public boolean isFlying() { return flying; }
    public void setFlying(boolean flying) { this.flying = flying; }
    
    public boolean isSneaking() { return sneaking; }
    public void setSneaking(boolean sneaking) { this.sneaking = sneaking; }
    
    public boolean isSprinting() { return sprinting; }
    public void setSprinting(boolean sprinting) { this.sprinting = sprinting; }
    
    public boolean isInWater() { return inWater; }
    public void setInWater(boolean inWater) { this.inWater = inWater; }
    
    public boolean isInLava() { return inLava; }
    public void setInLava(boolean inLava) { this.inLava = inLava; }
    
    public boolean isInWeb() { return inWeb; }
    public void setInWeb(boolean inWeb) { this.inWeb = inWeb; }
    
    public Map<String, ViolationData> getViolations() { return violations; }
}
