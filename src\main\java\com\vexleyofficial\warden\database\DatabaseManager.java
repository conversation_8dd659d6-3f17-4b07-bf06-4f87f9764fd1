package com.vexleyofficial.warden.database;

import com.vexleyofficial.warden.WardenAntiCheat;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.sql.*;
import java.util.concurrent.CompletableFuture;

/**
 * Manages database connections and operations
 */
public class DatabaseManager {
    
    private final WardenAntiCheat plugin;
    private Connection connection;
    private boolean initialized = false;
    
    public DatabaseManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Initialize database connection
     */
    public void initialize() {
        try {
            String databaseType = plugin.getConfigManager().getDatabaseType().toLowerCase();
            
            switch (databaseType) {
                case "sqlite":
                    initializeSQLite();
                    break;
                case "mysql":
                case "mariadb":
                    initializeMySQL();
                    break;
                case "postgresql":
                    initializePostgreSQL();
                    break;
                default:
                    plugin.getWardenLogger().warning("Unknown database type: " + databaseType + ", falling back to SQLite");
                    initializeSQLite();
                    break;
            }
            
            createTables();
            initialized = true;
            plugin.getWardenLogger().info("Database initialized successfully (" + databaseType + ")");
            
        } catch (SQLException e) {
            plugin.getWardenLogger().severe("Failed to initialize database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Initialize SQLite connection
     */
    private void initializeSQLite() throws SQLException {
        File dataFolder = plugin.getDataFolder();
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }
        
        String dbPath = new File(dataFolder, plugin.getConfigManager().getSqliteFile()).getAbsolutePath();
        String url = "jdbc:sqlite:" + dbPath;
        
        connection = DriverManager.getConnection(url);
        connection.setAutoCommit(true);
    }
    
    /**
     * Initialize MySQL connection
     */
    private void initializeMySQL() throws SQLException {
        String host = plugin.getConfigManager().getDbHost();
        int port = plugin.getConfigManager().getDbPort();
        String database = plugin.getConfigManager().getDbName();
        String username = plugin.getConfigManager().getDbUsername();
        String password = plugin.getConfigManager().getDbPassword();
        
        String url = String.format("*****************************************************", 
            host, port, database);
        
        connection = DriverManager.getConnection(url, username, password);
        connection.setAutoCommit(true);
    }
    
    /**
     * Initialize PostgreSQL connection
     */
    private void initializePostgreSQL() throws SQLException {
        String host = plugin.getConfigManager().getDbHost();
        int port = plugin.getConfigManager().getDbPort();
        String database = plugin.getConfigManager().getDbName();
        String username = plugin.getConfigManager().getDbUsername();
        String password = plugin.getConfigManager().getDbPassword();
        
        String url = String.format("jdbc:postgresql://%s:%d/%s", host, port, database);
        
        connection = DriverManager.getConnection(url, username, password);
        connection.setAutoCommit(true);
    }
    
    /**
     * Create necessary database tables
     */
    private void createTables() throws SQLException {
        // Players table
        String createPlayersTable = """
            CREATE TABLE IF NOT EXISTS warden_players (
                id INTEGER PRIMARY KEY %s,
                uuid VARCHAR(36) UNIQUE NOT NULL,
                name VARCHAR(16) NOT NULL,
                first_seen BIGINT NOT NULL,
                last_seen BIGINT NOT NULL,
                total_violations INTEGER DEFAULT 0
            )
            """.formatted(isMySQL() ? "AUTO_INCREMENT" : "AUTOINCREMENT");
        
        // Violations table
        String createViolationsTable = """
            CREATE TABLE IF NOT EXISTS warden_violations (
                id INTEGER PRIMARY KEY %s,
                player_uuid VARCHAR(36) NOT NULL,
                check_type VARCHAR(32) NOT NULL,
                confidence DOUBLE NOT NULL,
                timestamp BIGINT NOT NULL,
                data TEXT,
                FOREIGN KEY (player_uuid) REFERENCES warden_players(uuid)
            )
            """.formatted(isMySQL() ? "AUTO_INCREMENT" : "AUTOINCREMENT");
        
        // Punishments table
        String createPunishmentsTable = """
            CREATE TABLE IF NOT EXISTS warden_punishments (
                id INTEGER PRIMARY KEY %s,
                player_uuid VARCHAR(36) NOT NULL,
                punishment_type VARCHAR(16) NOT NULL,
                reason VARCHAR(255) NOT NULL,
                timestamp BIGINT NOT NULL,
                duration BIGINT DEFAULT 0,
                active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (player_uuid) REFERENCES warden_players(uuid)
            )
            """.formatted(isMySQL() ? "AUTO_INCREMENT" : "AUTOINCREMENT");
        
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createPlayersTable);
            stmt.execute(createViolationsTable);
            stmt.execute(createPunishmentsTable);
        }
        
        // Create indexes for better performance
        createIndexes();
    }
    
    /**
     * Create database indexes
     */
    private void createIndexes() throws SQLException {
        String[] indexes = {
            "CREATE INDEX IF NOT EXISTS idx_violations_player_uuid ON warden_violations(player_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_violations_timestamp ON warden_violations(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_violations_check_type ON warden_violations(check_type)",
            "CREATE INDEX IF NOT EXISTS idx_punishments_player_uuid ON warden_punishments(player_uuid)",
            "CREATE INDEX IF NOT EXISTS idx_punishments_active ON warden_punishments(active)"
        };
        
        try (Statement stmt = connection.createStatement()) {
            for (String index : indexes) {
                stmt.execute(index);
            }
        }
    }
    
    /**
     * Record a player in the database
     */
    public CompletableFuture<Void> recordPlayer(@NotNull String uuid, @NotNull String name) {
        return CompletableFuture.runAsync(() -> {
            if (!initialized) return;
            
            try {
                String sql = """
                    INSERT OR REPLACE INTO warden_players (uuid, name, first_seen, last_seen, total_violations)
                    VALUES (?, ?, 
                        COALESCE((SELECT first_seen FROM warden_players WHERE uuid = ?), ?),
                        ?, 
                        COALESCE((SELECT total_violations FROM warden_players WHERE uuid = ?), 0))
                    """;
                
                if (isMySQL()) {
                    sql = """
                        INSERT INTO warden_players (uuid, name, first_seen, last_seen, total_violations)
                        VALUES (?, ?, ?, ?, 0)
                        ON DUPLICATE KEY UPDATE 
                            name = VALUES(name),
                            last_seen = VALUES(last_seen)
                        """;
                }
                
                long currentTime = System.currentTimeMillis();
                
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setString(1, uuid);
                    stmt.setString(2, name);
                    
                    if (isMySQL()) {
                        stmt.setLong(3, currentTime);
                        stmt.setLong(4, currentTime);
                    } else {
                        stmt.setString(3, uuid);
                        stmt.setLong(4, currentTime);
                        stmt.setLong(5, currentTime);
                        stmt.setString(6, uuid);
                    }
                    
                    stmt.executeUpdate();
                }
            } catch (SQLException e) {
                plugin.getWardenLogger().error("Error recording player: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * Record a violation in the database
     */
    public CompletableFuture<Void> recordViolation(@NotNull String uuid, @NotNull String checkType, 
                                                   double confidence, @NotNull String data) {
        return CompletableFuture.runAsync(() -> {
            if (!initialized) return;
            
            try {
                String sql = "INSERT INTO warden_violations (player_uuid, check_type, confidence, timestamp, data) VALUES (?, ?, ?, ?, ?)";
                
                try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                    stmt.setString(1, uuid);
                    stmt.setString(2, checkType);
                    stmt.setDouble(3, confidence);
                    stmt.setLong(4, System.currentTimeMillis());
                    stmt.setString(5, data);
                    stmt.executeUpdate();
                }
                
                // Update total violations count
                String updateSql = "UPDATE warden_players SET total_violations = total_violations + 1 WHERE uuid = ?";
                try (PreparedStatement stmt = connection.prepareStatement(updateSql)) {
                    stmt.setString(1, uuid);
                    stmt.executeUpdate();
                }
                
            } catch (SQLException e) {
                plugin.getWardenLogger().error("Error recording violation: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * Check if using MySQL/MariaDB
     */
    private boolean isMySQL() {
        return plugin.getConfigManager().getDatabaseType().toLowerCase().contains("mysql") ||
               plugin.getConfigManager().getDatabaseType().toLowerCase().contains("mariadb");
    }
    
    /**
     * Close database connection
     */
    public void close() {
        if (connection != null) {
            try {
                connection.close();
                plugin.getWardenLogger().info("Database connection closed");
            } catch (SQLException e) {
                plugin.getWardenLogger().error("Error closing database connection: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * Get database connection
     */
    public Connection getConnection() {
        return connection;
    }
    
    /**
     * Check if database is initialized
     */
    public boolean isInitialized() {
        return initialized;
    }
}
