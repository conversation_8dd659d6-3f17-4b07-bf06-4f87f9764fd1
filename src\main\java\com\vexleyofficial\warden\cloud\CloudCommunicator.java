package com.vexleyofficial.warden.cloud;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.cloud.models.DetectionRequest;
import com.vexleyofficial.warden.cloud.models.DetectionResponse;
import com.vexleyofficial.warden.cloud.models.HeartbeatRequest;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Handles communication with Warden Cloud API
 */
public class CloudCommunicator {
    
    private static final String BASE_URL = "https://api.warden.anticheat";
    private static final String ENDPOINT_TEST = "/v1/test";
    private static final String ENDPOINT_DETECT = "/v1/detect";
    private static final String ENDPOINT_HEARTBEAT = "/v1/heartbeat";
    
    private final WardenAntiCheat plugin;
    private final OkHttpClient httpClient;
    private final Gson gson;
    private final String apiKey;
    private final String region;
    
    private boolean connected = false;
    private long lastHeartbeat = 0;
    
    public CloudCommunicator(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.gson = new Gson();
        this.apiKey = plugin.getConfigManager().getApiKey();
        this.region = plugin.getConfigManager().getCloudRegion();
        
        // Configure HTTP client
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(plugin.getConfigManager().getCloudTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(plugin.getConfigManager().getCloudTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(plugin.getConfigManager().getCloudTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * Test connection to Warden Cloud
     */
    public boolean testConnection() {
        if (!plugin.getConfigManager().isCloudEnabled()) {
            plugin.getWardenLogger().info("Cloud communication is disabled in config");
            return false;
        }
        
        if (!plugin.getConfigManager().isApiKeyConfigured()) {
            plugin.getWardenLogger().warning("API key not configured - please set your API key in config.yml");
            return false;
        }
        
        try {
            Request request = new Request.Builder()
                    .url(getRegionalUrl() + ENDPOINT_TEST)
                    .addHeader("Authorization", "Bearer " + apiKey)
                    .addHeader("User-Agent", "WardenAC/" + plugin.getDescription().getVersion())
                    .addHeader("Content-Type", "application/json")
                    .build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    connected = true;
                    plugin.getWardenLogger().info("Cloud connection test successful");
                    return true;
                } else {
                    plugin.getWardenLogger().warning("Cloud connection test failed: HTTP " + response.code());
                    if (response.body() != null) {
                        plugin.getWardenLogger().warning("Response: " + response.body().string());
                    }
                    return false;
                }
            }
        } catch (IOException e) {
            plugin.getWardenLogger().warning("Cloud connection test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Send detection data to cloud for analysis
     */
    public CompletableFuture<DetectionResponse> sendDetectionRequest(@NotNull DetectionRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            if (!connected || !plugin.getConfigManager().isCloudEnabled()) {
                return null;
            }
            
            try {
                String jsonBody = gson.toJson(request);
                RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json"));
                
                Request httpRequest = new Request.Builder()
                        .url(getRegionalUrl() + ENDPOINT_DETECT)
                        .post(body)
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("User-Agent", "WardenAC/" + plugin.getDescription().getVersion())
                        .addHeader("Content-Type", "application/json")
                        .build();
                
                try (Response response = httpClient.newCall(httpRequest).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        return gson.fromJson(responseBody, DetectionResponse.class);
                    } else {
                        plugin.getWardenLogger().warning("Detection request failed: HTTP " + response.code());
                        return null;
                    }
                }
            } catch (Exception e) {
                plugin.getWardenLogger().warning("Error sending detection request: " + e.getMessage());
                return null;
            }
        });
    }
    
    /**
     * Send heartbeat to maintain connection
     */
    public void sendHeartbeat() {
        if (!connected || !plugin.getConfigManager().isCloudEnabled()) {
            return;
        }
        
        CompletableFuture.runAsync(() -> {
            try {
                HeartbeatRequest heartbeat = new HeartbeatRequest(
                        plugin.getServer().getOnlinePlayers().size(),
                        System.currentTimeMillis(),
                        plugin.getDescription().getVersion()
                );
                
                String jsonBody = gson.toJson(heartbeat);
                RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json"));
                
                Request request = new Request.Builder()
                        .url(getRegionalUrl() + ENDPOINT_HEARTBEAT)
                        .post(body)
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("User-Agent", "WardenAC/" + plugin.getDescription().getVersion())
                        .addHeader("Content-Type", "application/json")
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        lastHeartbeat = System.currentTimeMillis();
                        if (plugin.getConfigManager().isDebugMode()) {
                            plugin.getWardenLogger().info("Heartbeat sent successfully");
                        }
                    } else {
                        plugin.getWardenLogger().warning("Heartbeat failed: HTTP " + response.code());
                    }
                }
            } catch (Exception e) {
                plugin.getWardenLogger().warning("Error sending heartbeat: " + e.getMessage());
            }
        });
    }
    
    /**
     * Get regional API URL based on configuration
     */
    private String getRegionalUrl() {
        switch (region.toLowerCase()) {
            case "us-east":
                return "https://us-east.api.warden.anticheat";
            case "eu-west":
                return "https://eu-west.api.warden.anticheat";
            case "asia-pacific":
                return "https://asia.api.warden.anticheat";
            case "auto":
            default:
                return BASE_URL; // Auto-routing
        }
    }
    
    /**
     * Shutdown the cloud communicator
     */
    public void shutdown() {
        connected = false;
        
        // Send final heartbeat
        if (plugin.getConfigManager().isCloudEnabled()) {
            try {
                JsonObject finalHeartbeat = new JsonObject();
                finalHeartbeat.addProperty("status", "shutdown");
                finalHeartbeat.addProperty("timestamp", System.currentTimeMillis());
                
                String jsonBody = gson.toJson(finalHeartbeat);
                RequestBody body = RequestBody.create(jsonBody, MediaType.get("application/json"));
                
                Request request = new Request.Builder()
                        .url(getRegionalUrl() + ENDPOINT_HEARTBEAT)
                        .post(body)
                        .addHeader("Authorization", "Bearer " + apiKey)
                        .addHeader("User-Agent", "WardenAC/" + plugin.getDescription().getVersion())
                        .addHeader("Content-Type", "application/json")
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    // Silent shutdown heartbeat
                }
            } catch (Exception e) {
                // Ignore errors during shutdown
            }
        }
        
        // Shutdown HTTP client
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
        
        plugin.getWardenLogger().info("Cloud communicator shutdown complete");
    }
    
    /**
     * Check if cloud is connected
     */
    public boolean isConnected() {
        return connected;
    }
    
    /**
     * Get last heartbeat timestamp
     */
    public long getLastHeartbeat() {
        return lastHeartbeat;
    }
    
    /**
     * Manually set connection status
     */
    public void setConnected(boolean connected) {
        this.connected = connected;
        plugin.setCloudConnected(connected);
    }
}
