package com.vexleyofficial.warden.listeners;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.jetbrains.annotations.NotNull;

/**
 * Handles world interaction events for anti-cheat detection
 */
public class WorldListener implements Listener {
    
    private final WardenAntiCheat plugin;
    
    public WorldListener(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = false)
    public void onBlockBreak(@NotNull BlockBreakEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        // Skip if player has bypass permission
        if (player.hasPermission("warden.bypass")) {
            return;
        }
        
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        
        // Use advanced world detection modules
        plugin.getDetectionManager().getCheckManager().getWorldChecks().checkFastBreak(player, event, playerData);
        
        // Trigger X-Ray analysis periodically
        if (playerData.getViolations().size() % 10 == 0) { // Every 10 violations
            plugin.getDetectionManager().getCheckManager().getWorldChecks().checkXRay(player, playerData);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = false)
    public void onBlockPlace(@NotNull BlockPlaceEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        // Skip if player has bypass permission
        if (player.hasPermission("warden.bypass")) {
            return;
        }
        
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        
        // Use advanced world detection modules
        plugin.getDetectionManager().getCheckManager().getWorldChecks().checkFastPlace(player, event, playerData);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(@NotNull PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        
        // Check for chest ESP if interacting with containers
        if (event.getClickedBlock() != null) {
            plugin.getDetectionManager().getCheckManager().getWorldChecks().checkChestESP(player, playerData, event.getClickedBlock());
        }
    }
}
