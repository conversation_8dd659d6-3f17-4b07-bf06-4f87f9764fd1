package com.vexleyofficial.warden.detection.checks;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;

/**
 * Advanced movement detection checks
 */
public class MovementChecks {
    
    private final WardenAntiCheat plugin;
    
    public MovementChecks(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Advanced fly detection with physics simulation
     */
    public void checkAdvancedFly(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        if (player.isFlying() || player.getAllowFlight()) {
            return; // Player is allowed to fly
        }
        
        double deltaY = to.getY() - from.getY();
        
        // Check for sustained upward movement without ground contact
        if (deltaY > 0 && !player.isOnGround() && !playerData.isInWater()) {
            checkSustainedFlight(player, playerData, deltaY);
        }
        
        // Check for hovering (staying at same Y level)
        if (Math.abs(deltaY) < 0.01 && !player.isOnGround() && !playerData.isInWater()) {
            checkHovering(player, playerData);
        }
        
        // Check for impossible vertical acceleration
        checkVerticalAcceleration(player, playerData, deltaY);
        
        // Check for glide violations
        checkGlide(player, playerData, from, to);
    }
    
    /**
     * Check for sustained flight without touching ground
     */
    private void checkSustainedFlight(@NotNull Player player, @NotNull PlayerData playerData, double deltaY) {
        // Track air time
        long currentTime = System.currentTimeMillis();
        long airTime = currentTime - playerData.getLastMoveTime();
        
        if (airTime > 3000 && deltaY > 0.1) { // 3 seconds in air moving up
            double confidence = Math.min(airTime / 5000.0, 1.0);
            playerData.addViolation("fly_sustained", confidence);
            
            plugin.getDetectionManager().processCheck(player, "fly", confidence, airTime, deltaY);
        }
    }
    
    /**
     * Check for hovering in mid-air
     */
    private void checkHovering(@NotNull Player player, @NotNull PlayerData playerData) {
        // Count consecutive hover ticks
        // In a full implementation, you'd track this in PlayerData
        
        double confidence = 0.8; // High confidence for hovering
        playerData.addViolation("fly_hover", confidence);
        
        plugin.getDetectionManager().processCheck(player, "fly", confidence, "hovering");
    }
    
    /**
     * Check for impossible vertical acceleration
     */
    private void checkVerticalAcceleration(@NotNull Player player, @NotNull PlayerData playerData, double deltaY) {
        double lastDeltaY = playerData.getLastVelocityY();
        double acceleration = deltaY - lastDeltaY;
        
        // Gravity should cause downward acceleration
        if (acceleration > 0.1 && !playerData.isInWater()) {
            double confidence = Math.min(acceleration / 0.5, 1.0);
            playerData.addViolation("fly_acceleration", confidence);
            
            plugin.getDetectionManager().processCheck(player, "fly", confidence, acceleration);
        }
    }
    
    /**
     * Check for glide violations (slow falling)
     */
    private void checkGlide(@NotNull Player player, @NotNull PlayerData playerData, @NotNull Location from, @NotNull Location to) {
        double deltaY = to.getY() - from.getY();
        
        if (deltaY < 0 && !player.isOnGround() && !playerData.isInWater()) {
            double fallSpeed = Math.abs(deltaY);
            double expectedFallSpeed = 0.08; // Minimum expected fall speed
            
            if (fallSpeed < expectedFallSpeed) {
                double confidence = 1.0 - (fallSpeed / expectedFallSpeed);
                playerData.addViolation("glide", confidence);
                
                plugin.getDetectionManager().processCheck(player, "glide", confidence, fallSpeed, expectedFallSpeed);
            }
        }
    }
    
    /**
     * Advanced speed detection with environmental factors
     */
    public void checkAdvancedSpeed(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        double horizontalDistance = calculateHorizontalDistance(from, to);
        long timeDiff = System.currentTimeMillis() - playerData.getLastMoveTime();
        
        if (timeDiff <= 0) return;
        
        double speed = horizontalDistance / (timeDiff / 1000.0); // blocks per second
        double maxSpeed = calculateMaxSpeed(player, playerData);
        
        if (speed > maxSpeed) {
            double confidence = Math.min((speed - maxSpeed) / maxSpeed, 1.0);
            playerData.addViolation("speed", confidence);
            
            plugin.getDetectionManager().processCheck(player, "speed", confidence, speed, maxSpeed);
        }
        
        // Check for timer violations (client-side speed up)
        checkTimer(player, playerData, timeDiff);
    }
    
    /**
     * Check for timer violations (client speeding up)
     */
    private void checkTimer(@NotNull Player player, @NotNull PlayerData playerData, long timeDiff) {
        // Expected time between moves should be around 50ms (20 TPS)
        long expectedTime = 50;
        
        if (timeDiff < expectedTime * 0.8) { // 20% faster than expected
            double confidence = 1.0 - ((double) timeDiff / expectedTime);
            playerData.addViolation("timer", confidence);
            
            plugin.getDetectionManager().processCheck(player, "timer", confidence, timeDiff, expectedTime);
        }
    }
    
    /**
     * Bhop (bunny hop) detection
     */
    public void checkBhop(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        double deltaY = to.getY() - from.getY();
        double horizontalSpeed = playerData.getHorizontalSpeed();
        
        // Bhop characteristics: jumping while maintaining high horizontal speed
        if (deltaY > 0.3 && horizontalSpeed > 4.0 && !playerData.isInWater()) {
            // Check if player just landed and immediately jumped again
            if (player.isOnGround()) {
                double confidence = Math.min(horizontalSpeed / 6.0, 1.0);
                playerData.addViolation("bhop", confidence);
                
                plugin.getDetectionManager().processCheck(player, "bhop", confidence, horizontalSpeed, deltaY);
            }
        }
    }
    
    /**
     * Jesus/Water walking detection
     */
    public void checkJesus(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        if (!playerData.isInWater()) return;
        
        double deltaY = to.getY() - from.getY();
        double horizontalSpeed = playerData.getHorizontalSpeed();
        
        // Walking on water: maintaining Y level or moving up while in water
        if (deltaY >= -0.1 && horizontalSpeed > 0.2) {
            double confidence = Math.min(horizontalSpeed / 0.5, 1.0);
            playerData.addViolation("jesus", confidence);
            
            plugin.getDetectionManager().processCheck(player, "jesus", confidence, horizontalSpeed, deltaY);
        }
    }
    
    /**
     * Step detection (climbing blocks instantly)
     */
    public void checkStep(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        double deltaY = to.getY() - from.getY();
        double horizontalDistance = calculateHorizontalDistance(from, to);
        
        // Normal step height is 0.6 blocks
        if (deltaY > 0.6 && deltaY < 2.0 && horizontalDistance > 0.1) {
            double confidence = (deltaY - 0.6) / 1.4;
            playerData.addViolation("step", confidence);
            
            plugin.getDetectionManager().processCheck(player, "step", confidence, deltaY);
        }
    }
    
    /**
     * Phase/NoClip detection
     */
    public void checkPhase(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        if (isMovingThroughSolids(from, to)) {
            double confidence = 0.9; // High confidence for phase detection
            playerData.addViolation("phase", confidence);
            
            plugin.getDetectionManager().processCheck(player, "phase", confidence, from, to);
        }
    }
    
    /**
     * Spider detection (climbing walls)
     */
    public void checkSpider(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        double deltaY = to.getY() - from.getY();
        
        if (deltaY > 0.1 && isAgainstWall(player)) {
            double confidence = Math.min(deltaY / 1.0, 1.0);
            playerData.addViolation("spider", confidence);
            
            plugin.getDetectionManager().processCheck(player, "spider", confidence, deltaY);
        }
    }
    
    /**
     * NoFall detection
     */
    public void checkNoFall(@NotNull Player player, @NotNull Location from, @NotNull Location to, @NotNull PlayerData playerData) {
        double deltaY = to.getY() - from.getY();
        
        // If player was falling and suddenly stops taking fall damage
        if (deltaY < -3.0 && player.getFallDistance() == 0) {
            double confidence = Math.min(Math.abs(deltaY) / 10.0, 1.0);
            playerData.addViolation("nofall", confidence);
            
            plugin.getDetectionManager().processCheck(player, "nofall", confidence, deltaY, player.getFallDistance());
        }
    }
    
    // Helper methods
    
    private double calculateHorizontalDistance(@NotNull Location from, @NotNull Location to) {
        double deltaX = to.getX() - from.getX();
        double deltaZ = to.getZ() - from.getZ();
        return Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
    }
    
    private double calculateMaxSpeed(@NotNull Player player, @NotNull PlayerData playerData) {
        double baseSpeed = 4.3; // Normal walking speed
        
        if (playerData.isSprinting()) {
            baseSpeed = 5.6;
        }
        
        if (playerData.isSneaking()) {
            baseSpeed *= 0.3;
        }
        
        if (playerData.isInWater()) {
            baseSpeed *= 0.3;
        }
        
        // Check for speed effects
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (effect.getType().equals(PotionEffectType.SPEED)) {
                baseSpeed *= (1.0 + (effect.getAmplifier() + 1) * 0.2);
            } else if (effect.getType().equals(PotionEffectType.SLOW)) {
                baseSpeed *= (1.0 - (effect.getAmplifier() + 1) * 0.15);
            }
        }
        
        return baseSpeed * 1.1; // Add tolerance
    }
    
    private boolean isMovingThroughSolids(@NotNull Location from, @NotNull Location to) {
        double distance = from.distance(to);
        if (distance < 0.1) return false;
        
        int steps = (int) Math.ceil(distance * 4);
        
        for (int i = 1; i < steps; i++) {
            double ratio = (double) i / steps;
            
            double x = from.getX() + (to.getX() - from.getX()) * ratio;
            double y = from.getY() + (to.getY() - from.getY()) * ratio;
            double z = from.getZ() + (to.getZ() - from.getZ()) * ratio;
            
            Location checkLoc = new Location(from.getWorld(), x, y, z);
            Block block = checkLoc.getBlock();
            
            if (block.getType().isSolid() && !isPassableBlock(block.getType())) {
                return true;
            }
        }
        
        return false;
    }
    
    private boolean isAgainstWall(@NotNull Player player) {
        Location loc = player.getLocation();
        Block[] adjacentBlocks = {
            loc.clone().add(1, 0, 0).getBlock(),
            loc.clone().add(-1, 0, 0).getBlock(),
            loc.clone().add(0, 0, 1).getBlock(),
            loc.clone().add(0, 0, -1).getBlock()
        };
        
        for (Block block : adjacentBlocks) {
            if (block.getType().isSolid()) {
                return true;
            }
        }
        
        return false;
    }
    
    private boolean isPassableBlock(@NotNull Material material) {
        return material == Material.AIR || 
               material.name().contains("WATER") ||
               material.name().contains("LAVA") ||
               material.name().contains("SIGN") ||
               material.name().contains("TORCH");
    }
}
