package com.vexleyofficial.warden.listeners;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerToggleFlightEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.event.player.PlayerToggleSprintEvent;
import org.jetbrains.annotations.NotNull;

/**
 * Handles movement-related events for anti-cheat detection
 */
public class MovementListener implements Listener {
    
    private final WardenAntiCheat plugin;
    
    public MovementListener(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(@NotNull PlayerMoveEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        // Skip if player has bypass permission
        if (player.hasPermission("warden.bypass")) {
            return;
        }
        
        // Update player data
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.updateMovement(event.getTo(), player.isOnGround());
        
        // Update environmental flags
        updateEnvironmentalFlags(player, playerData);
        
        // Process advanced movement checks
        plugin.getDetectionManager().processAdvancedMovementCheck(player, event.getFrom(), event.getTo());

        // Use advanced movement detection modules
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkAdvancedFly(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkAdvancedSpeed(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkBhop(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkJesus(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkStep(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkPhase(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkSpider(player, event.getFrom(), event.getTo(), playerData);
        plugin.getDetectionManager().getCheckManager().getMovementChecks().checkNoFall(player, event.getFrom(), event.getTo(), playerData);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerToggleFlight(@NotNull PlayerMoveEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.setFlying(player.isFlying());
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerToggleSneak(@NotNull PlayerToggleSneakEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.setSneaking(event.isSneaking());
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerToggleSprint(@NotNull PlayerToggleSprintEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.setSprinting(event.isSprinting());
    }
    
    /**
     * Update environmental flags for the player
     */
    private void updateEnvironmentalFlags(@NotNull Player player, @NotNull PlayerData playerData) {
        playerData.setInWater(player.getLocation().getBlock().isLiquid());
        playerData.setInLava(player.getLocation().getBlock().getType().name().contains("LAVA"));
        playerData.setInWeb(player.getLocation().getBlock().getType().name().contains("WEB"));
    }
    

}
