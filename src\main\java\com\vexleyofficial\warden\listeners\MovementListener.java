package com.vexleyofficial.warden.listeners;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerToggleFlightEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.event.player.PlayerToggleSprintEvent;
import org.jetbrains.annotations.NotNull;

/**
 * Handles movement-related events for anti-cheat detection
 */
public class MovementListener implements Listener {
    
    private final WardenAntiCheat plugin;
    
    public MovementListener(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(@NotNull PlayerMoveEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        // Skip if player has bypass permission
        if (player.hasPermission("warden.bypass")) {
            return;
        }
        
        // Update player data
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.updateMovement(event.getTo(), player.isOnGround());
        
        // Update environmental flags
        updateEnvironmentalFlags(player, playerData);
        
        // Process movement checks
        plugin.getDetectionManager().processCheck(player, "movement", 
            event.getFrom(), event.getTo(), player.isOnGround());
        
        // Check for specific movement violations
        checkFly(player, playerData);
        checkSpeed(player, playerData);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerToggleFlight(@NotNull PlayerMoveEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.setFlying(player.isFlying());
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerToggleSneak(@NotNull PlayerToggleSneakEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.setSneaking(event.isSneaking());
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerToggleSprint(@NotNull PlayerToggleSprintEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.setSprinting(event.isSprinting());
    }
    
    /**
     * Update environmental flags for the player
     */
    private void updateEnvironmentalFlags(@NotNull Player player, @NotNull PlayerData playerData) {
        playerData.setInWater(player.getLocation().getBlock().isLiquid());
        playerData.setInLava(player.getLocation().getBlock().getType().name().contains("LAVA"));
        playerData.setInWeb(player.getLocation().getBlock().getType().name().contains("WEB"));
    }
    
    /**
     * Check for fly violations
     */
    private void checkFly(@NotNull Player player, @NotNull PlayerData playerData) {
        if (player.isFlying() || player.getAllowFlight()) {
            return; // Player is allowed to fly
        }
        
        if (playerData.getCurrentLocation() == null || playerData.getLastLocation() == null) {
            return;
        }
        
        double yDiff = playerData.getCurrentLocation().getY() - playerData.getLastLocation().getY();
        
        // Simple fly check - if player is moving up without being on ground
        if (yDiff > 0.1 && !playerData.isLastOnGround() && !playerData.isInWater()) {
            double confidence = Math.min(yDiff * 2, 1.0); // Simple confidence calculation
            
            plugin.getDetectionManager().processCheck(player, "fly", confidence, yDiff);
            
            // Apply direct mitigation if confidence is high
            if (confidence > 0.8) {
                plugin.getMitigationManager().applyDirectMitigation(player, "fly", confidence);
            }
        }
    }
    
    /**
     * Check for speed violations
     */
    private void checkSpeed(@NotNull Player player, @NotNull PlayerData playerData) {
        double horizontalSpeed = playerData.getHorizontalSpeed();
        
        // Base speed limits (blocks per second)
        double maxWalkSpeed = 4.3; // Normal walking speed
        double maxSprintSpeed = 5.6; // Normal sprinting speed
        
        double maxSpeed = playerData.isSprinting() ? maxSprintSpeed : maxWalkSpeed;
        
        // Adjust for environmental factors
        if (playerData.isInWater()) {
            maxSpeed *= 0.3; // Swimming is slower
        } else if (playerData.isSneaking()) {
            maxSpeed *= 0.3; // Sneaking is slower
        }
        
        if (horizontalSpeed > maxSpeed) {
            double confidence = Math.min((horizontalSpeed - maxSpeed) / maxSpeed, 1.0);
            
            plugin.getDetectionManager().processCheck(player, "speed", confidence, horizontalSpeed, maxSpeed);
            
            // Apply direct mitigation if confidence is high
            if (confidence > 0.6) {
                plugin.getMitigationManager().applyDirectMitigation(player, "speed", confidence);
            }
        }
    }
}
