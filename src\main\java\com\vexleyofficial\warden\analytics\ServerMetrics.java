package com.vexleyofficial.warden.analytics;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.utils.VersionChecker;
import org.bukkit.Bukkit;
import org.jetbrains.annotations.NotNull;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Server performance and health metrics collection
 */
public class ServerMetrics {
    
    private final ConcurrentLinkedQueue<Double> tpsHistory;
    private final ConcurrentLinkedQueue<Long> memoryHistory;
    private final ConcurrentLinkedQueue<Double> cpuHistory;
    private final ConcurrentLinkedQueue<Integer> playerCountHistory;
    
    // Current metrics
    private double currentTPS;
    private long currentMemoryUsage;
    private double currentCPUUsage;
    private int currentPlayerCount;
    
    // Derived metrics
    private double averageTPS;
    private double minTPS;
    private double maxTPS;
    private long averageMemoryUsage;
    private double averageCPUUsage;
    private double averagePlayerCount;
    
    // Performance indicators
    private boolean isLagging;
    private boolean isMemoryPressure;
    private boolean isCPUPressure;
    private String performanceStatus;
    
    public ServerMetrics() {
        this.tpsHistory = new ConcurrentLinkedQueue<>();
        this.memoryHistory = new ConcurrentLinkedQueue<>();
        this.cpuHistory = new ConcurrentLinkedQueue<>();
        this.playerCountHistory = new ConcurrentLinkedQueue<>();
        
        this.performanceStatus = "UNKNOWN";
    }
    
    /**
     * Update all server metrics
     */
    public void update(@NotNull WardenAntiCheat plugin) {
        updateTPS();
        updateMemoryUsage();
        updateCPUUsage();
        updatePlayerCount();
        updateDerivedMetrics();
        updatePerformanceStatus();
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug(String.format(
                "Server Metrics - TPS: %.1f, Memory: %dMB, CPU: %.1f%%, Players: %d",
                currentTPS, currentMemoryUsage / 1024 / 1024, currentCPUUsage * 100, currentPlayerCount
            ));
        }
    }
    
    /**
     * Update TPS metrics
     */
    private void updateTPS() {
        currentTPS = VersionChecker.getTPS();
        addToLimitedQueue(tpsHistory, currentTPS, 120); // 2 hours of data (30s intervals)
    }
    
    /**
     * Update memory usage metrics
     */
    private void updateMemoryUsage() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        currentMemoryUsage = memoryBean.getHeapMemoryUsage().getUsed();
        addToLimitedQueue(memoryHistory, currentMemoryUsage, 120);
    }
    
    /**
     * Update CPU usage metrics
     */
    private void updateCPUUsage() {
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        
        if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
            com.sun.management.OperatingSystemMXBean sunOsBean = (com.sun.management.OperatingSystemMXBean) osBean;
            currentCPUUsage = sunOsBean.getProcessCpuLoad();
        } else {
            // Fallback for non-Sun JVMs
            currentCPUUsage = osBean.getSystemLoadAverage() / osBean.getAvailableProcessors();
        }
        
        if (currentCPUUsage < 0) currentCPUUsage = 0; // Handle unavailable data
        addToLimitedQueue(cpuHistory, currentCPUUsage, 120);
    }
    
    /**
     * Update player count metrics
     */
    private void updatePlayerCount() {
        currentPlayerCount = Bukkit.getOnlinePlayers().size();
        addToLimitedQueue(playerCountHistory, currentPlayerCount, 120);
    }
    
    /**
     * Update derived metrics from historical data
     */
    private void updateDerivedMetrics() {
        // TPS metrics
        if (!tpsHistory.isEmpty()) {
            averageTPS = tpsHistory.stream().mapToDouble(Double::doubleValue).average().orElse(20.0);
            minTPS = tpsHistory.stream().mapToDouble(Double::doubleValue).min().orElse(20.0);
            maxTPS = tpsHistory.stream().mapToDouble(Double::doubleValue).max().orElse(20.0);
        }
        
        // Memory metrics
        if (!memoryHistory.isEmpty()) {
            averageMemoryUsage = memoryHistory.stream().mapToLong(Long::longValue).sum() / memoryHistory.size();
        }
        
        // CPU metrics
        if (!cpuHistory.isEmpty()) {
            averageCPUUsage = cpuHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        }
        
        // Player count metrics
        if (!playerCountHistory.isEmpty()) {
            averagePlayerCount = playerCountHistory.stream().mapToInt(Integer::intValue).average().orElse(0.0);
        }
        
        // Performance indicators
        isLagging = currentTPS < 18.0;
        isMemoryPressure = getCurrentMemoryUsagePercent() > 80.0;
        isCPUPressure = currentCPUUsage > 0.8;
    }
    
    /**
     * Update overall performance status
     */
    private void updatePerformanceStatus() {
        if (isLagging || isMemoryPressure || isCPUPressure) {
            if (currentTPS < 15.0 || getCurrentMemoryUsagePercent() > 90.0 || currentCPUUsage > 0.9) {
                performanceStatus = "CRITICAL";
            } else {
                performanceStatus = "WARNING";
            }
        } else if (currentTPS > 19.5 && getCurrentMemoryUsagePercent() < 60.0 && currentCPUUsage < 0.5) {
            performanceStatus = "EXCELLENT";
        } else {
            performanceStatus = "GOOD";
        }
    }
    
    /**
     * Get current memory usage as percentage
     */
    public double getCurrentMemoryUsagePercent() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        if (maxMemory <= 0) return 0.0;
        return (currentMemoryUsage * 100.0) / maxMemory;
    }
    
    /**
     * Get TPS trend over the last 10 minutes
     */
    public String getTPSTrend() {
        if (tpsHistory.size() < 20) return "INSUFFICIENT_DATA";
        
        List<Double> recent = new ArrayList<>(tpsHistory).subList(Math.max(0, tpsHistory.size() - 20), tpsHistory.size());
        double recentAvg = recent.stream().mapToDouble(Double::doubleValue).average().orElse(20.0);
        
        if (recentAvg > averageTPS + 1.0) {
            return "IMPROVING";
        } else if (recentAvg < averageTPS - 1.0) {
            return "DECLINING";
        } else {
            return "STABLE";
        }
    }
    
    /**
     * Get memory trend
     */
    public String getMemoryTrend() {
        if (memoryHistory.size() < 20) return "INSUFFICIENT_DATA";
        
        List<Long> recent = new ArrayList<>(memoryHistory).subList(Math.max(0, memoryHistory.size() - 20), memoryHistory.size());
        double recentAvg = recent.stream().mapToLong(Long::longValue).average().orElse(0.0);
        
        if (recentAvg > averageMemoryUsage * 1.1) {
            return "INCREASING";
        } else if (recentAvg < averageMemoryUsage * 0.9) {
            return "DECREASING";
        } else {
            return "STABLE";
        }
    }
    
    /**
     * Get performance recommendations
     */
    public List<String> getPerformanceRecommendations() {
        List<String> recommendations = new ArrayList<>();
        
        if (isLagging) {
            recommendations.add("Server TPS is low - consider reducing world size or player count");
        }
        
        if (isMemoryPressure) {
            recommendations.add("High memory usage detected - consider increasing heap size or reducing plugins");
        }
        
        if (isCPUPressure) {
            recommendations.add("High CPU usage detected - consider optimizing plugins or upgrading hardware");
        }
        
        if (currentPlayerCount > averagePlayerCount * 1.5) {
            recommendations.add("Player count is significantly above average - monitor for performance impact");
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("Server performance is optimal");
        }
        
        return recommendations;
    }
    
    /**
     * Get metrics snapshot for reporting
     */
    public Map<String, Object> getSnapshot() {
        Map<String, Object> snapshot = new HashMap<>();
        
        // Current metrics
        snapshot.put("current_tps", currentTPS);
        snapshot.put("current_memory_mb", currentMemoryUsage / 1024 / 1024);
        snapshot.put("current_memory_percent", getCurrentMemoryUsagePercent());
        snapshot.put("current_cpu_percent", currentCPUUsage * 100);
        snapshot.put("current_players", currentPlayerCount);
        
        // Average metrics
        snapshot.put("average_tps", averageTPS);
        snapshot.put("min_tps", minTPS);
        snapshot.put("max_tps", maxTPS);
        snapshot.put("average_memory_mb", averageMemoryUsage / 1024 / 1024);
        snapshot.put("average_cpu_percent", averageCPUUsage * 100);
        snapshot.put("average_players", averagePlayerCount);
        
        // Status indicators
        snapshot.put("performance_status", performanceStatus);
        snapshot.put("is_lagging", isLagging);
        snapshot.put("is_memory_pressure", isMemoryPressure);
        snapshot.put("is_cpu_pressure", isCPUPressure);
        
        // Trends
        snapshot.put("tps_trend", getTPSTrend());
        snapshot.put("memory_trend", getMemoryTrend());
        
        // Recommendations
        snapshot.put("recommendations", getPerformanceRecommendations());
        
        return snapshot;
    }
    
    /**
     * Add item to queue with size limit
     */
    private <T> void addToLimitedQueue(@NotNull ConcurrentLinkedQueue<T> queue, T item, int maxSize) {
        queue.offer(item);
        while (queue.size() > maxSize) {
            queue.poll();
        }
    }
    
    // Getters
    public double getCurrentTPS() { return currentTPS; }
    public long getCurrentMemoryUsage() { return currentMemoryUsage; }
    public double getCurrentCPUUsage() { return currentCPUUsage; }
    public int getCurrentPlayerCount() { return currentPlayerCount; }
    
    public double getAverageTPS() { return averageTPS; }
    public double getMinTPS() { return minTPS; }
    public double getMaxTPS() { return maxTPS; }
    public long getAverageMemoryUsage() { return averageMemoryUsage; }
    public double getAverageCPUUsage() { return averageCPUUsage; }
    public double getAveragePlayerCount() { return averagePlayerCount; }
    
    public boolean isLagging() { return isLagging; }
    public boolean isMemoryPressure() { return isMemoryPressure; }
    public boolean isCPUPressure() { return isCPUPressure; }
    public String getPerformanceStatus() { return performanceStatus; }
}
