package com.vexleyofficial.warden;

import com.vexleyofficial.warden.cloud.CloudCommunicator;
import com.vexleyofficial.warden.commands.WardenCommand;
import com.vexleyofficial.warden.commands.WardenAlertsCommand;
import com.vexleyofficial.warden.config.ConfigManager;
import com.vexleyofficial.warden.config.MessageManager;
import com.vexleyofficial.warden.database.DatabaseManager;
import com.vexleyofficial.warden.detection.DetectionManager;
import com.vexleyofficial.warden.listeners.PlayerListener;
import com.vexleyofficial.warden.listeners.MovementListener;
import com.vexleyofficial.warden.listeners.CombatListener;
import com.vexleyofficial.warden.mitigation.MitigationManager;
import com.vexleyofficial.warden.utils.Logger;
import com.vexleyofficial.warden.utils.VersionChecker;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.CompletableFuture;

/**
 * Warden Anti-Cheat - Next-Generation Cloud-Powered Anti-Cheat Solution
 * 
 * <AUTHOR>
 * @version 1.0.0-BETA
 * @since 1.0.0
 */
public final class WardenAntiCheat extends JavaPlugin {
    
    private static WardenAntiCheat instance;
    
    // Core managers
    private ConfigManager configManager;
    private MessageManager messageManager;
    private DatabaseManager databaseManager;
    private CloudCommunicator cloudCommunicator;
    private DetectionManager detectionManager;
    private MitigationManager mitigationManager;
    private Logger wardenLogger;
    
    // Plugin state
    private boolean cloudConnected = false;
    private long startTime;
    
    @Override
    public void onLoad() {
        instance = this;
        startTime = System.currentTimeMillis();
        
        // Initialize logger first
        wardenLogger = new Logger(this);
        wardenLogger.info("Loading Warden Anti-Cheat v" + getDescription().getVersion());
        
        // Check server version compatibility
        if (!VersionChecker.isSupported()) {
            wardenLogger.severe("Unsupported server version! Warden supports 1.8.x - 1.21.5");
            wardenLogger.severe("Current version: " + VersionChecker.getServerVersion());
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        wardenLogger.info("Server version " + VersionChecker.getServerVersion() + " is supported");
    }
    
    @Override
    public void onEnable() {
        try {
            // Initialize configuration
            initializeConfig();
            
            // Initialize database
            initializeDatabase();
            
            // Initialize cloud communication
            initializeCloud();
            
            // Initialize detection system
            initializeDetection();
            
            // Initialize mitigation system
            initializeMitigation();
            
            // Register commands
            registerCommands();
            
            // Register listeners
            registerListeners();
            
            // Start background tasks
            startBackgroundTasks();
            
            long loadTime = System.currentTimeMillis() - startTime;
            wardenLogger.info("Warden Anti-Cheat enabled successfully in " + loadTime + "ms");
            wardenLogger.info("Cloud status: " + (cloudConnected ? "Connected" : "Offline"));
            
        } catch (Exception e) {
            wardenLogger.severe("Failed to enable Warden Anti-Cheat: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    @Override
    public void onDisable() {
        wardenLogger.info("Disabling Warden Anti-Cheat...");
        
        // Shutdown detection manager
        if (detectionManager != null) {
            detectionManager.shutdown();
        }
        
        // Shutdown cloud communicator
        if (cloudCommunicator != null) {
            cloudCommunicator.shutdown();
        }
        
        // Close database connections
        if (databaseManager != null) {
            databaseManager.close();
        }
        
        wardenLogger.info("Warden Anti-Cheat disabled successfully");
    }
    
    private void initializeConfig() {
        wardenLogger.info("Initializing configuration...");
        configManager = new ConfigManager(this);
        messageManager = new MessageManager(this);
        
        // Save default configs if they don't exist
        saveDefaultConfig();
        configManager.loadConfig();
        messageManager.loadMessages();
    }
    
    private void initializeDatabase() {
        wardenLogger.info("Initializing database...");
        databaseManager = new DatabaseManager(this);
        databaseManager.initialize();
    }
    
    private void initializeCloud() {
        wardenLogger.info("Initializing cloud communication...");
        cloudCommunicator = new CloudCommunicator(this);
        
        // Test cloud connection asynchronously
        CompletableFuture.supplyAsync(() -> {
            try {
                return cloudCommunicator.testConnection();
            } catch (Exception e) {
                wardenLogger.warning("Cloud connection test failed: " + e.getMessage());
                return false;
            }
        }).thenAccept(connected -> {
            cloudConnected = connected;
            if (connected) {
                wardenLogger.info("Successfully connected to Warden Cloud");
            } else {
                wardenLogger.warning("Failed to connect to Warden Cloud - running in offline mode");
            }
        });
    }
    
    private void initializeDetection() {
        wardenLogger.info("Initializing detection system...");
        detectionManager = new DetectionManager(this);
        detectionManager.initialize();
    }
    
    private void initializeMitigation() {
        wardenLogger.info("Initializing mitigation system...");
        mitigationManager = new MitigationManager(this);
        mitigationManager.initialize();
    }
    
    private void registerCommands() {
        wardenLogger.info("Registering commands...");
        getCommand("warden").setExecutor(new WardenCommand(this));
        getCommand("wardenalerts").setExecutor(new WardenAlertsCommand(this));
    }
    
    private void registerListeners() {
        wardenLogger.info("Registering event listeners...");
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new MovementListener(this), this);
        getServer().getPluginManager().registerEvents(new CombatListener(this), this);
    }
    
    private void startBackgroundTasks() {
        wardenLogger.info("Starting background tasks...");
        
        // TPS monitoring task
        Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
            // Monitor server TPS and adjust detection sensitivity
            double tps = VersionChecker.getTPS();
            if (tps < configManager.getMinTPS()) {
                detectionManager.setLowTPSMode(true);
            } else {
                detectionManager.setLowTPSMode(false);
            }
        }, 20L, 100L); // Every 5 seconds
        
        // Cloud heartbeat task
        if (cloudConnected) {
            Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
                cloudCommunicator.sendHeartbeat();
            }, 20L, 1200L); // Every minute
        }
    }
    
    /**
     * Reload the plugin configuration
     */
    public void reload() {
        wardenLogger.info("Reloading Warden configuration...");
        
        reloadConfig();
        configManager.loadConfig();
        messageManager.loadMessages();
        
        if (detectionManager != null) {
            detectionManager.reload();
        }
        
        wardenLogger.info("Configuration reloaded successfully");
    }
    
    // Getters for managers
    public static WardenAntiCheat getInstance() {
        return instance;
    }
    
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    public MessageManager getMessageManager() {
        return messageManager;
    }
    
    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }
    
    public CloudCommunicator getCloudCommunicator() {
        return cloudCommunicator;
    }
    
    public DetectionManager getDetectionManager() {
        return detectionManager;
    }
    
    public MitigationManager getMitigationManager() {
        return mitigationManager;
    }
    
    public Logger getWardenLogger() {
        return wardenLogger;
    }
    
    public boolean isCloudConnected() {
        return cloudConnected;
    }
    
    public void setCloudConnected(boolean connected) {
        this.cloudConnected = connected;
    }
}
