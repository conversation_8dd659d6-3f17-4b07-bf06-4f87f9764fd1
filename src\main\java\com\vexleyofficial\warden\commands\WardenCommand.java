package com.vexleyofficial.warden.commands;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.utils.VersionChecker;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Main command handler for /warden
 */
public class WardenCommand implements CommandExecutor, TabCompleter {
    
    private final WardenAntiCheat plugin;
    
    public WardenCommand(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (!sender.hasPermission("warden.admin")) {
            if (sender instanceof Player) {
                plugin.getMessageManager().sendPrefixedMessage((Player) sender, "no-permission");
            } else {
                sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            }
            return true;
        }
        
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                sendHelp(sender);
                break;
                
            case "info":
                sendInfo(sender);
                break;
                
            case "reload":
                reloadConfig(sender);
                break;
                
            case "check":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "Usage: /warden check <player>");
                    return true;
                }
                checkPlayer(sender, args[1]);
                break;
                
            case "version":
                sendVersion(sender);
                break;
                
            case "status":
                sendStatus(sender);
                break;
                
            default:
                if (sender instanceof Player) {
                    plugin.getMessageManager().sendPrefixedMessage((Player) sender, "invalid-command");
                } else {
                    sender.sendMessage(ChatColor.RED + "Invalid command. Use /warden help for assistance.");
                }
                break;
        }
        
        return true;
    }
    
    private void sendHelp(@NotNull CommandSender sender) {
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Warden Anti-Cheat Commands");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.GRAY + "/warden help " + ChatColor.WHITE + "- Show this help menu");
        sender.sendMessage(ChatColor.GRAY + "/warden info " + ChatColor.WHITE + "- Show plugin information");
        sender.sendMessage(ChatColor.GRAY + "/warden reload " + ChatColor.WHITE + "- Reload configuration");
        sender.sendMessage(ChatColor.GRAY + "/warden check <player> " + ChatColor.WHITE + "- Check player status");
        sender.sendMessage(ChatColor.GRAY + "/warden version " + ChatColor.WHITE + "- Show version information");
        sender.sendMessage(ChatColor.GRAY + "/warden status " + ChatColor.WHITE + "- Show system status");
        sender.sendMessage(ChatColor.GRAY + "/wardenalerts " + ChatColor.WHITE + "- Toggle alerts");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void sendInfo(@NotNull CommandSender sender) {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("version", plugin.getDescription().getVersion());
        placeholders.put("author", plugin.getDescription().getAuthors().get(0));
        placeholders.put("status", plugin.isCloudConnected() ? ChatColor.GREEN + "Connected" : ChatColor.RED + "Offline");
        placeholders.put("count", String.valueOf(Bukkit.getOnlinePlayers().size()));
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Warden Anti-Cheat Information");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        if (sender instanceof Player) {
            plugin.getMessageManager().sendMessage((Player) sender, "info-version", placeholders);
            plugin.getMessageManager().sendMessage((Player) sender, "info-author", placeholders);
            plugin.getMessageManager().sendMessage((Player) sender, "info-cloud-status", placeholders);
            plugin.getMessageManager().sendMessage((Player) sender, "info-players-monitored", placeholders);
        } else {
            sender.sendMessage(ChatColor.GRAY + "Version: " + ChatColor.WHITE + placeholders.get("version"));
            sender.sendMessage(ChatColor.GRAY + "Author: " + ChatColor.WHITE + placeholders.get("author"));
            sender.sendMessage(ChatColor.GRAY + "Cloud Status: " + placeholders.get("status"));
            sender.sendMessage(ChatColor.GRAY + "Players Monitored: " + ChatColor.WHITE + placeholders.get("count"));
        }
        
        sender.sendMessage(ChatColor.GRAY + "Server: " + ChatColor.WHITE + VersionChecker.getServerInfo());
        sender.sendMessage(ChatColor.GRAY + "TPS: " + ChatColor.WHITE + String.format("%.1f", VersionChecker.getTPS()));
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void reloadConfig(@NotNull CommandSender sender) {
        try {
            plugin.reload();
            
            if (sender instanceof Player) {
                plugin.getMessageManager().sendPrefixedMessage((Player) sender, "config-reloaded");
            } else {
                sender.sendMessage(ChatColor.GREEN + "Configuration reloaded successfully.");
            }
        } catch (Exception e) {
            Map<String, String> placeholders = new HashMap<>();
            placeholders.put("error", e.getMessage());
            
            if (sender instanceof Player) {
                plugin.getMessageManager().sendPrefixedMessage((Player) sender, "config-reload-error", placeholders);
            } else {
                sender.sendMessage(ChatColor.RED + "Error reloading configuration: " + e.getMessage());
            }
        }
    }
    
    private void checkPlayer(@NotNull CommandSender sender, @NotNull String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            Map<String, String> placeholders = new HashMap<>();
            placeholders.put("player", playerName);
            
            if (sender instanceof Player) {
                plugin.getMessageManager().sendPrefixedMessage((Player) sender, "player-not-found", placeholders);
            } else {
                sender.sendMessage(ChatColor.RED + "Player '" + playerName + "' not found.");
            }
            return;
        }
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Player Check: " + target.getName());
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.GRAY + "UUID: " + ChatColor.WHITE + target.getUniqueId());
        sender.sendMessage(ChatColor.GRAY + "Location: " + ChatColor.WHITE + 
            String.format("%.1f, %.1f, %.1f (%s)", target.getLocation().getX(), 
                target.getLocation().getY(), target.getLocation().getZ(), target.getWorld().getName()));
        sender.sendMessage(ChatColor.GRAY + "Ping: " + ChatColor.WHITE + getPing(target) + "ms");
        sender.sendMessage(ChatColor.GRAY + "Whitelisted: " + 
            (plugin.getConfigManager().isPlayerWhitelisted(target.getName()) ? 
                ChatColor.GREEN + "Yes" : ChatColor.RED + "No"));
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void sendVersion(@NotNull CommandSender sender) {
        sender.sendMessage(ChatColor.RED + "Warden Anti-Cheat v" + plugin.getDescription().getVersion());
        sender.sendMessage(ChatColor.GRAY + "by " + plugin.getDescription().getAuthors().get(0));
        sender.sendMessage(ChatColor.GRAY + "Website: " + plugin.getDescription().getWebsite());
    }
    
    private void sendStatus(@NotNull CommandSender sender) {
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Warden System Status");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.GRAY + "Cloud Status: " + 
            (plugin.isCloudConnected() ? ChatColor.GREEN + "Connected" : ChatColor.RED + "Offline"));
        sender.sendMessage(ChatColor.GRAY + "Detection System: " + ChatColor.GREEN + "Active");
        sender.sendMessage(ChatColor.GRAY + "Real-time Mitigation: " + 
            (plugin.getConfigManager().isRealTimeMitigation() ? ChatColor.GREEN + "Enabled" : ChatColor.RED + "Disabled"));
        sender.sendMessage(ChatColor.GRAY + "Server TPS: " + ChatColor.WHITE + String.format("%.1f", VersionChecker.getTPS()));
        sender.sendMessage(ChatColor.GRAY + "Players Online: " + ChatColor.WHITE + Bukkit.getOnlinePlayers().size());
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private int getPing(@NotNull Player player) {
        try {
            // Try modern method first
            return player.getPing();
        } catch (Exception e) {
            // Fallback for older versions
            return 0;
        }
    }
    
    @Override
    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String[] args) {
        if (!sender.hasPermission("warden.admin")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("help", "info", "reload", "check", "version", "status");
            List<String> completions = new ArrayList<>();
            
            for (String subCommand : subCommands) {
                if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(subCommand);
                }
            }
            
            return completions;
        }
        
        if (args.length == 2 && args[0].equalsIgnoreCase("check")) {
            List<String> playerNames = new ArrayList<>();
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.getName().toLowerCase().startsWith(args[1].toLowerCase())) {
                    playerNames.add(player.getName());
                }
            }
            return playerNames;
        }
        
        return new ArrayList<>();
    }
}
