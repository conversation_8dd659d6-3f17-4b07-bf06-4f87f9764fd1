package com.vexleyofficial.warden.cloud.models;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

/**
 * Response model from cloud detection analysis
 */
public class DetectionResponse {
    
    private final String requestId;
    private final boolean violation;
    private final double confidence;
    private final String cheatType;
    private final String severity;
    private final String recommendedAction;
    private final List<MitigationAction> mitigationActions;
    private final Map<String, Object> metadata;
    private final long processingTime;
    
    public DetectionResponse(@NotNull String requestId,
                           boolean violation,
                           double confidence,
                           @Nullable String cheatType,
                           @Nullable String severity,
                           @Nullable String recommendedAction,
                           @Nullable List<MitigationAction> mitigationActions,
                           @Nullable Map<String, Object> metadata,
                           long processingTime) {
        this.requestId = requestId;
        this.violation = violation;
        this.confidence = confidence;
        this.cheatType = cheatType;
        this.severity = severity;
        this.recommendedAction = recommendedAction;
        this.mitigationActions = mitigationActions;
        this.metadata = metadata;
        this.processingTime = processingTime;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public boolean isViolation() {
        return violation;
    }
    
    public double getConfidence() {
        return confidence;
    }
    
    @Nullable
    public String getCheatType() {
        return cheatType;
    }
    
    @Nullable
    public String getSeverity() {
        return severity;
    }
    
    @Nullable
    public String getRecommendedAction() {
        return recommendedAction;
    }
    
    @Nullable
    public List<MitigationAction> getMitigationActions() {
        return mitigationActions;
    }
    
    @Nullable
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public long getProcessingTime() {
        return processingTime;
    }
    
    /**
     * Mitigation action to be applied in real-time
     */
    public static class MitigationAction {
        private final String type;
        private final Map<String, Object> parameters;
        private final int priority;
        private final String description;
        
        public MitigationAction(@NotNull String type,
                              @NotNull Map<String, Object> parameters,
                              int priority,
                              @Nullable String description) {
            this.type = type;
            this.parameters = parameters;
            this.priority = priority;
            this.description = description;
        }
        
        public String getType() {
            return type;
        }
        
        public Map<String, Object> getParameters() {
            return parameters;
        }
        
        public int getPriority() {
            return priority;
        }
        
        @Nullable
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Check if confidence meets minimum threshold
     */
    public boolean meetsConfidenceThreshold(double threshold) {
        return confidence >= threshold;
    }
    
    /**
     * Check if this is a severe violation
     */
    public boolean isSevere() {
        return "severe".equalsIgnoreCase(severity) || "critical".equalsIgnoreCase(severity);
    }
    
    /**
     * Get confidence as percentage
     */
    public int getConfidencePercentage() {
        return (int) Math.round(confidence * 100);
    }
}
