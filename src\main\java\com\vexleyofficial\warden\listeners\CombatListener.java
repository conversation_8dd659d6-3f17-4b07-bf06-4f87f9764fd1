package com.vexleyofficial.warden.listeners;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.jetbrains.annotations.NotNull;

/**
 * Handles combat-related events for anti-cheat detection
 */
public class CombatListener implements Listener {
    
    private final WardenAntiCheat plugin;
    
    public CombatListener(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDamageByEntity(@NotNull EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player attacker = (Player) event.getDamager();
        Entity target = event.getEntity();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(attacker.getName())) {
            return;
        }
        
        // Skip if player has bypass permission
        if (attacker.hasPermission("warden.bypass")) {
            return;
        }
        
        // Calculate reach distance
        double reach = attacker.getLocation().distance(target.getLocation());
        
        // Update player data
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(attacker);
        playerData.updateCombat(reach);
        
        // Use advanced combat detection modules
        plugin.getDetectionManager().getCheckManager().getCombatChecks().checkAdvancedReach(attacker, target, playerData);
        plugin.getDetectionManager().getCheckManager().getCombatChecks().checkKillAura(attacker, target, playerData);
        plugin.getDetectionManager().getCheckManager().getCombatChecks().checkCriticals(attacker, target, playerData);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(@NotNull PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        // Track clicks for CPS detection
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.updateClick();
        
        // Use advanced auto-clicker detection
        plugin.getDetectionManager().getCheckManager().getCombatChecks().checkAutoClicker(player, playerData);
    }
    

}
