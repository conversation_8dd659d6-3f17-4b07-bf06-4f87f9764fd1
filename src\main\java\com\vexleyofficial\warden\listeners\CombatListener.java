package com.vexleyofficial.warden.listeners;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.jetbrains.annotations.NotNull;

/**
 * Handles combat-related events for anti-cheat detection
 */
public class CombatListener implements Listener {
    
    private final WardenAntiCheat plugin;
    
    public CombatListener(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDamageByEntity(@NotNull EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player attacker = (Player) event.getDamager();
        Entity target = event.getEntity();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(attacker.getName())) {
            return;
        }
        
        // Skip if player has bypass permission
        if (attacker.hasPermission("warden.bypass")) {
            return;
        }
        
        // Calculate reach distance
        double reach = attacker.getLocation().distance(target.getLocation());
        
        // Update player data
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(attacker);
        playerData.updateCombat(reach);
        
        // Process combat checks
        plugin.getDetectionManager().processCheck(attacker, "combat", reach, target);
        
        // Check for specific combat violations
        checkReach(attacker, playerData, reach, event);
        checkKillAura(attacker, playerData);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(@NotNull PlayerInteractEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is whitelisted
        if (plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        // Track clicks for CPS detection
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        playerData.updateClick();
        
        // Check for auto-clicker
        checkAutoClicker(player, playerData);
    }
    
    /**
     * Check for reach violations
     */
    private void checkReach(@NotNull Player attacker, @NotNull PlayerData playerData, 
                           double reach, @NotNull EntityDamageByEntityEvent event) {
        double maxReach = 3.0; // Standard Minecraft reach distance
        
        if (reach > maxReach) {
            double confidence = Math.min((reach - maxReach) / maxReach, 1.0);
            
            plugin.getDetectionManager().processCheck(attacker, "reach", confidence, reach, maxReach);
            
            // Apply direct mitigation
            if (confidence > 0.5) {
                event.setCancelled(true); // Cancel the attack
                plugin.getMitigationManager().applyDirectMitigation(attacker, "reach", confidence);
                
                if (plugin.getConfigManager().isDebugMode()) {
                    plugin.getWardenLogger().debug("Cancelled reach attack from " + attacker.getName() + 
                        " (reach: " + String.format("%.2f", reach) + ")");
                }
            }
        }
    }
    
    /**
     * Check for kill aura violations
     */
    private void checkKillAura(@NotNull Player attacker, @NotNull PlayerData playerData) {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastAttack = currentTime - playerData.getLastAttackTime();
        
        // Check for suspiciously fast attacks
        if (timeSinceLastAttack < 100) { // Less than 100ms between attacks
            double confidence = 1.0 - (timeSinceLastAttack / 100.0);
            
            plugin.getDetectionManager().processCheck(attacker, "killaura", confidence, timeSinceLastAttack);
            
            if (confidence > 0.7) {
                plugin.getMitigationManager().applyDirectMitigation(attacker, "killaura", confidence);
            }
        }
        
        // Check attack patterns over time
        if (playerData.getAttackCount() > 10) {
            long timeSpan = currentTime - playerData.getLastAttackTime();
            double attackRate = playerData.getAttackCount() / (timeSpan / 1000.0); // attacks per second
            
            if (attackRate > 15) { // More than 15 attacks per second is suspicious
                double confidence = Math.min(attackRate / 20.0, 1.0);
                
                plugin.getDetectionManager().processCheck(attacker, "killaura", confidence, attackRate);
                
                if (confidence > 0.8) {
                    plugin.getMitigationManager().applyDirectMitigation(attacker, "killaura", confidence);
                }
            }
        }
    }
    
    /**
     * Check for auto-clicker violations
     */
    private void checkAutoClicker(@NotNull Player player, @NotNull PlayerData playerData) {
        int cps = playerData.getCps();
        long currentTime = System.currentTimeMillis();
        long timeSinceLastClick = currentTime - playerData.getLastClickTime();
        
        // Check for suspiciously high CPS
        if (cps > 20) { // More than 20 CPS is suspicious
            double confidence = Math.min(cps / 30.0, 1.0);
            
            plugin.getDetectionManager().processCheck(player, "autoclicker", confidence, cps);
            
            if (confidence > 0.7) {
                plugin.getMitigationManager().applyDirectMitigation(player, "autoclicker", confidence);
            }
        }
        
        // Check for perfectly consistent click timing (bot-like behavior)
        if (timeSinceLastClick > 0 && timeSinceLastClick < 10) {
            // Very consistent timing might indicate a bot
            double consistency = 1.0 - (timeSinceLastClick / 50.0);
            
            if (consistency > 0.9) {
                plugin.getDetectionManager().processCheck(player, "autoclicker", consistency, timeSinceLastClick);
                
                if (consistency > 0.95) {
                    plugin.getMitigationManager().applyDirectMitigation(player, "autoclicker", consistency);
                }
            }
        }
    }
}
