package com.vexleyofficial.warden.commands;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.analytics.AnalyticsReport;
import com.vexleyofficial.warden.analytics.PlayerProfile;
import com.vexleyofficial.warden.monitoring.MonitoringEvent;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Advanced analytics and monitoring command
 */
public class WardenAnalyticsCommand implements CommandExecutor, TabCompleter {
    
    private final WardenAntiCheat plugin;
    
    public WardenAnalyticsCommand(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (!sender.hasPermission("warden.analytics")) {
            sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
            return true;
        }
        
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "report":
                generateReport(sender);
                break;
                
            case "player":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "Usage: /wardenanalytics player <name>");
                    return true;
                }
                showPlayerAnalytics(sender, args[1]);
                break;
                
            case "alerts":
                showActiveAlerts(sender);
                break;
                
            case "events":
                int limit = args.length > 1 ? parseInt(args[1], 10) : 10;
                showRecentEvents(sender, limit);
                break;
                
            case "metrics":
                showServerMetrics(sender);
                break;
                
            case "trends":
                showCheatTrends(sender);
                break;
                
            case "export":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "Usage: /wardenanalytics export <player>");
                    return true;
                }
                exportPlayerData(sender, args[1]);
                break;
                
            default:
                sendHelp(sender);
                break;
        }
        
        return true;
    }
    
    private void sendHelp(@NotNull CommandSender sender) {
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Warden Analytics Commands");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics report " + ChatColor.WHITE + "- Generate analytics report");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics player <name> " + ChatColor.WHITE + "- Show player analytics");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics alerts " + ChatColor.WHITE + "- Show active alerts");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics events [limit] " + ChatColor.WHITE + "- Show recent events");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics metrics " + ChatColor.WHITE + "- Show server metrics");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics trends " + ChatColor.WHITE + "- Show cheat trends");
        sender.sendMessage(ChatColor.GRAY + "/wardenanalytics export <player> " + ChatColor.WHITE + "- Export player data");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void generateReport(@NotNull CommandSender sender) {
        sender.sendMessage(ChatColor.YELLOW + "Generating analytics report...");
        
        AnalyticsReport report = plugin.getAnalyticsEngine().generateAnalyticsReport();
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Warden Analytics Report");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        sender.sendMessage(ChatColor.GRAY + "Total Players: " + ChatColor.WHITE + report.getTotalPlayers());
        sender.sendMessage(ChatColor.GRAY + "High Risk Players: " + ChatColor.RED + report.getHighRiskPlayers());
        
        if (report.getCheatStatistics() != null && !report.getCheatStatistics().isEmpty()) {
            sender.sendMessage(ChatColor.GRAY + "Top Violations:");
            report.getCheatStatistics().entrySet().stream()
                .sorted((a, b) -> Long.compare(b.getValue(), a.getValue()))
                .limit(5)
                .forEach(entry -> sender.sendMessage(ChatColor.GRAY + "  " + entry.getKey() + ": " + ChatColor.WHITE + entry.getValue()));
        }
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void showPlayerAnalytics(@NotNull CommandSender sender, @NotNull String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player not found or offline.");
            return;
        }
        
        PlayerProfile profile = plugin.getAnalyticsEngine().getPlayerProfiles().get(target.getUniqueId());
        if (profile == null) {
            sender.sendMessage(ChatColor.RED + "No analytics data available for this player.");
            return;
        }
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Player Analytics: " + target.getName());
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        // Risk assessment
        double riskScore = profile.getRiskScore();
        ChatColor riskColor = riskScore > 0.7 ? ChatColor.RED : riskScore > 0.4 ? ChatColor.YELLOW : ChatColor.GREEN;
        sender.sendMessage(ChatColor.GRAY + "Risk Score: " + riskColor + String.format("%.2f", riskScore));
        
        // Movement metrics
        sender.sendMessage(ChatColor.GRAY + "Average Speed: " + ChatColor.WHITE + String.format("%.2f", profile.getAverageSpeed()));
        sender.sendMessage(ChatColor.GRAY + "Max Speed: " + ChatColor.WHITE + String.format("%.2f", profile.getMaxSpeed()));
        sender.sendMessage(ChatColor.GRAY + "Movement Consistency: " + ChatColor.WHITE + String.format("%.2f", profile.getMovementConsistency()));
        
        // Combat metrics
        sender.sendMessage(ChatColor.GRAY + "Average CPS: " + ChatColor.WHITE + String.format("%.1f", profile.getAverageCPS()));
        sender.sendMessage(ChatColor.GRAY + "Max CPS: " + ChatColor.WHITE + profile.getMaxCPS());
        sender.sendMessage(ChatColor.GRAY + "Attack Accuracy: " + ChatColor.WHITE + String.format("%.2f", profile.getAttackAccuracy()));
        
        // Anomalies
        List<String> anomalies = profile.getAnomalies();
        if (!anomalies.isEmpty()) {
            sender.sendMessage(ChatColor.GRAY + "Anomalies: " + ChatColor.RED + String.join(", ", anomalies));
        }
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void showActiveAlerts(@NotNull CommandSender sender) {
        Set<String> activeAlerts = plugin.getMonitoringSystem().getActiveAlerts();
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Active Alerts");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        if (activeAlerts.isEmpty()) {
            sender.sendMessage(ChatColor.GREEN + "No active alerts");
        } else {
            for (String alert : activeAlerts) {
                sender.sendMessage(ChatColor.RED + "• " + alert);
            }
        }
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void showRecentEvents(@NotNull CommandSender sender, int limit) {
        List<MonitoringEvent> events = plugin.getMonitoringSystem().getRecentEvents(limit);
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Recent Events (Last " + limit + ")");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        if (events.isEmpty()) {
            sender.sendMessage(ChatColor.GREEN + "No recent events");
        } else {
            for (MonitoringEvent event : events) {
                ChatColor severityColor = switch (event.getSeverity()) {
                    case LOW -> ChatColor.GREEN;
                    case MEDIUM -> ChatColor.YELLOW;
                    case HIGH -> ChatColor.GOLD;
                    case CRITICAL -> ChatColor.RED;
                };
                
                sender.sendMessage(severityColor + "[" + event.getSeverity() + "] " + 
                    ChatColor.GRAY + event.getAlertType() + ": " + ChatColor.WHITE + event.getMessage());
            }
        }
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void showServerMetrics(@NotNull CommandSender sender) {
        Map<String, Object> metrics = plugin.getAnalyticsEngine().getServerMetrics().getSnapshot();
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Server Metrics");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        sender.sendMessage(ChatColor.GRAY + "TPS: " + ChatColor.WHITE + metrics.get("current_tps"));
        sender.sendMessage(ChatColor.GRAY + "Memory: " + ChatColor.WHITE + metrics.get("current_memory_percent") + "%");
        sender.sendMessage(ChatColor.GRAY + "CPU: " + ChatColor.WHITE + metrics.get("current_cpu_percent") + "%");
        sender.sendMessage(ChatColor.GRAY + "Players: " + ChatColor.WHITE + metrics.get("current_players"));
        sender.sendMessage(ChatColor.GRAY + "Status: " + ChatColor.WHITE + metrics.get("performance_status"));
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void showCheatTrends(@NotNull CommandSender sender) {
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        sender.sendMessage(ChatColor.RED + "                    Cheat Trends");
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
        
        plugin.getAnalyticsEngine().getCheatStatistics().entrySet().stream()
            .sorted((a, b) -> Long.compare(b.getValue().getRecentViolations(), a.getValue().getRecentViolations()))
            .limit(10)
            .forEach(entry -> {
                String trend = entry.getValue().isTrendingUp() ? "↗" : 
                              entry.getValue().isTrendingDown() ? "↘" : "→";
                ChatColor trendColor = entry.getValue().isTrendingUp() ? ChatColor.RED : 
                                     entry.getValue().isTrendingDown() ? ChatColor.GREEN : ChatColor.YELLOW;
                
                sender.sendMessage(ChatColor.GRAY + entry.getKey() + ": " + 
                    ChatColor.WHITE + entry.getValue().getRecentViolations() + " " +
                    trendColor + trend);
            });
        
        sender.sendMessage(ChatColor.DARK_RED + "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
    }
    
    private void exportPlayerData(@NotNull CommandSender sender, @NotNull String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Player not found or offline.");
            return;
        }
        
        // Generate ML training data
        Map<String, Object> trainingData = plugin.getAnalyticsEngine().generateMLTrainingData(
            target, plugin.getDetectionManager().getPlayerData(target));
        
        sender.sendMessage(ChatColor.GREEN + "Exported " + trainingData.size() + " data points for " + playerName);
        sender.sendMessage(ChatColor.GRAY + "Data exported to console (in production, this would save to file)");
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().info("ML Training Data for " + playerName + ": " + trainingData);
        }
    }
    
    private int parseInt(String str, int defaultValue) {
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    @Override
    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String[] args) {
        if (!sender.hasPermission("warden.analytics")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("report", "player", "alerts", "events", "metrics", "trends", "export");
            return subCommands.stream()
                .filter(cmd -> cmd.toLowerCase().startsWith(args[0].toLowerCase()))
                .collect(Collectors.toList());
        }
        
        if (args.length == 2 && (args[0].equalsIgnoreCase("player") || args[0].equalsIgnoreCase("export"))) {
            return Bukkit.getOnlinePlayers().stream()
                .map(Player::getName)
                .filter(name -> name.toLowerCase().startsWith(args[1].toLowerCase()))
                .collect(Collectors.toList());
        }
        
        return new ArrayList<>();
    }
}
