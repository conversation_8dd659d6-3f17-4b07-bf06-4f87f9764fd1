package com.vexleyofficial.warden.detection;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.checks.CheckManager;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages all detection systems and player data
 */
public class DetectionManager {
    
    private final WardenAntiCheat plugin;
    private final Map<UUID, PlayerData> playerDataMap;
    private final CheckManager checkManager;
    private final ExecutorService detectionExecutor;
    private final ScheduledExecutorService scheduledExecutor;
    
    private boolean lowTPSMode = false;
    private boolean enabled = true;
    
    public DetectionManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.playerDataMap = new ConcurrentHashMap<>();
        this.checkManager = new CheckManager(plugin);
        
        // Initialize thread pools
        int threadPoolSize = plugin.getConfigManager().getThreadPoolSize();
        this.detectionExecutor = Executors.newFixedThreadPool(threadPoolSize);
        this.scheduledExecutor = Executors.newScheduledThreadPool(2);
    }
    
    /**
     * Initialize the detection system
     */
    public void initialize() {
        plugin.getWardenLogger().info("Initializing detection system...");
        
        checkManager.initialize();
        
        // Start cleanup task
        scheduledExecutor.scheduleAtFixedRate(this::cleanupPlayerData, 30, 30, TimeUnit.SECONDS);
        
        plugin.getWardenLogger().info("Detection system initialized successfully");
    }
    
    /**
     * Get or create player data
     */
    @NotNull
    public PlayerData getPlayerData(@NotNull Player player) {
        return playerDataMap.computeIfAbsent(player.getUniqueId(), 
            uuid -> new PlayerData(player.getUniqueId(), player.getName()));
    }
    
    /**
     * Remove player data when player leaves
     */
    public void removePlayerData(@NotNull UUID playerId) {
        PlayerData data = playerDataMap.remove(playerId);
        if (data != null) {
            data.cleanup();
        }
    }
    
    /**
     * Process a detection check asynchronously
     */
    public void processCheck(@NotNull Player player, @NotNull String checkType, @NotNull Object... data) {
        if (!enabled || plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        if (plugin.getConfigManager().isAsyncProcessing()) {
            detectionExecutor.submit(() -> processCheckSync(player, checkType, data));
        } else {
            processCheckSync(player, checkType, data);
        }
    }
    
    /**
     * Process a detection check synchronously
     */
    private void processCheckSync(@NotNull Player player, @NotNull String checkType, @NotNull Object... data) {
        try {
            PlayerData playerData = getPlayerData(player);
            
            // Rate limiting
            if (!playerData.canProcessCheck(plugin.getConfigManager().getMaxChecksPerSecond())) {
                return;
            }
            
            // Skip checks in low TPS mode if configured
            if (lowTPSMode && !isHighPriorityCheck(checkType)) {
                return;
            }
            
            checkManager.processCheck(player, playerData, checkType, data);
            
        } catch (Exception e) {
            plugin.getWardenLogger().error("Error processing check " + checkType + " for " + player.getName(), e);
        }
    }
    
    /**
     * Check if a check type is high priority (should run even in low TPS)
     */
    private boolean isHighPriorityCheck(@NotNull String checkType) {
        return checkType.equals("fly") || checkType.equals("speed") || checkType.equals("killaura");
    }
    
    /**
     * Clean up old player data
     */
    private void cleanupPlayerData() {
        long cutoffTime = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5);
        
        playerDataMap.entrySet().removeIf(entry -> {
            PlayerData data = entry.getValue();
            if (data.getLastActivity() < cutoffTime) {
                data.cleanup();
                return true;
            }
            return false;
        });
    }
    
    /**
     * Reload detection configuration
     */
    public void reload() {
        plugin.getWardenLogger().info("Reloading detection system...");
        checkManager.reload();
        plugin.getWardenLogger().info("Detection system reloaded");
    }
    
    /**
     * Set low TPS mode
     */
    public void setLowTPSMode(boolean lowTPSMode) {
        if (this.lowTPSMode != lowTPSMode) {
            this.lowTPSMode = lowTPSMode;
            if (lowTPSMode) {
                plugin.getWardenLogger().warning("Entering low TPS mode - some checks disabled");
            } else {
                plugin.getWardenLogger().info("Exiting low TPS mode - all checks enabled");
            }
        }
    }
    
    /**
     * Enable or disable the detection system
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        plugin.getWardenLogger().info("Detection system " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Shutdown the detection system
     */
    public void shutdown() {
        plugin.getWardenLogger().info("Shutting down detection system...");
        
        enabled = false;
        
        // Shutdown executors
        detectionExecutor.shutdown();
        scheduledExecutor.shutdown();
        
        try {
            if (!detectionExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                detectionExecutor.shutdownNow();
            }
            if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            detectionExecutor.shutdownNow();
            scheduledExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // Clean up all player data
        for (PlayerData data : playerDataMap.values()) {
            data.cleanup();
        }
        playerDataMap.clear();
        
        plugin.getWardenLogger().info("Detection system shutdown complete");
    }
    
    // Getters
    public CheckManager getCheckManager() {
        return checkManager;
    }
    
    public boolean isLowTPSMode() {
        return lowTPSMode;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public int getPlayerDataCount() {
        return playerDataMap.size();
    }
}
