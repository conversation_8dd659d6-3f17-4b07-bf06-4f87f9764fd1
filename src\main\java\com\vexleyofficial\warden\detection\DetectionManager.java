package com.vexleyofficial.warden.detection;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.checks.CheckManager;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.jetbrains.annotations.NotNull;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manages all detection systems and player data
 */
public class DetectionManager {
    
    private final WardenAntiCheat plugin;
    private final Map<UUID, PlayerData> playerDataMap;
    private final CheckManager checkManager;
    private final ExecutorService detectionExecutor;
    private final ScheduledExecutorService scheduledExecutor;
    
    private boolean lowTPSMode = false;
    private boolean enabled = true;
    
    public DetectionManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.playerDataMap = new ConcurrentHashMap<>();
        this.checkManager = new CheckManager(plugin);
        
        // Initialize thread pools
        int threadPoolSize = plugin.getConfigManager().getThreadPoolSize();
        this.detectionExecutor = Executors.newFixedThreadPool(threadPoolSize);
        this.scheduledExecutor = Executors.newScheduledThreadPool(2);
    }
    
    /**
     * Initialize the detection system
     */
    public void initialize() {
        plugin.getWardenLogger().info("Initializing detection system...");
        
        checkManager.initialize();
        
        // Start cleanup task
        scheduledExecutor.scheduleAtFixedRate(this::cleanupPlayerData, 30, 30, TimeUnit.SECONDS);
        
        plugin.getWardenLogger().info("Detection system initialized successfully");
    }
    
    /**
     * Get or create player data
     */
    @NotNull
    public PlayerData getPlayerData(@NotNull Player player) {
        return playerDataMap.computeIfAbsent(player.getUniqueId(), 
            uuid -> new PlayerData(player.getUniqueId(), player.getName()));
    }
    
    /**
     * Remove player data when player leaves
     */
    public void removePlayerData(@NotNull UUID playerId) {
        PlayerData data = playerDataMap.remove(playerId);
        if (data != null) {
            data.cleanup();
        }
    }
    
    /**
     * Process a detection check asynchronously
     */
    public void processCheck(@NotNull Player player, @NotNull String checkType, @NotNull Object... data) {
        if (!enabled || plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }
        
        if (plugin.getConfigManager().isAsyncProcessing()) {
            detectionExecutor.submit(() -> processCheckSync(player, checkType, data));
        } else {
            processCheckSync(player, checkType, data);
        }
    }
    
    /**
     * Process a detection check synchronously
     */
    private void processCheckSync(@NotNull Player player, @NotNull String checkType, @NotNull Object... data) {
        try {
            PlayerData playerData = getPlayerData(player);
            
            // Rate limiting
            if (!playerData.canProcessCheck(plugin.getConfigManager().getMaxChecksPerSecond())) {
                return;
            }
            
            // Skip checks in low TPS mode if configured
            if (lowTPSMode && !isHighPriorityCheck(checkType)) {
                return;
            }
            
            checkManager.processCheck(player, playerData, checkType, data);
            
        } catch (Exception e) {
            plugin.getWardenLogger().error("Error processing check " + checkType + " for " + player.getName(), e);
        }
    }
    
    /**
     * Check if a check type is high priority (should run even in low TPS)
     */
    private boolean isHighPriorityCheck(@NotNull String checkType) {
        return checkType.equals("fly") || checkType.equals("speed") || checkType.equals("killaura") ||
               checkType.equals("reach") || checkType.equals("criticals") || checkType.equals("velocity");
    }

    /**
     * Process advanced movement check with physics validation
     */
    public void processAdvancedMovementCheck(@NotNull Player player, @NotNull Location from, @NotNull Location to) {
        if (!enabled || plugin.getConfigManager().isPlayerWhitelisted(player.getName())) {
            return;
        }

        PlayerData playerData = getPlayerData(player);

        // Advanced movement analysis
        if (plugin.getConfigManager().isAsyncProcessing()) {
            detectionExecutor.submit(() -> {
                analyzeMovementPhysics(player, playerData, from, to);
                analyzeMovementPatterns(player, playerData, from, to);
                analyzeEnvironmentalFactors(player, playerData, from, to);
            });
        } else {
            analyzeMovementPhysics(player, playerData, from, to);
            analyzeMovementPatterns(player, playerData, from, to);
            analyzeEnvironmentalFactors(player, playerData, from, to);
        }
    }

    /**
     * Analyze movement physics for impossible movements
     */
    private void analyzeMovementPhysics(@NotNull Player player, @NotNull PlayerData playerData,
                                      @NotNull Location from, @NotNull Location to) {
        try {
            // Calculate movement vectors
            double deltaX = to.getX() - from.getX();
            double deltaY = to.getY() - from.getY();
            double deltaZ = to.getZ() - from.getZ();

            double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
            double verticalDistance = Math.abs(deltaY);

            // Check for impossible horizontal movement
            double maxHorizontalSpeed = calculateMaxHorizontalSpeed(player, playerData);
            if (horizontalDistance > maxHorizontalSpeed) {
                double confidence = Math.min((horizontalDistance - maxHorizontalSpeed) / maxHorizontalSpeed, 1.0);
                processCheck(player, "speed", confidence, horizontalDistance, maxHorizontalSpeed);
            }

            // Check for impossible vertical movement (fly detection)
            if (deltaY > 0 && !player.isFlying() && !playerData.isInWater()) {
                double maxVerticalSpeed = calculateMaxVerticalSpeed(player, playerData);
                if (deltaY > maxVerticalSpeed) {
                    double confidence = Math.min((deltaY - maxVerticalSpeed) / maxVerticalSpeed, 1.0);
                    processCheck(player, "fly", confidence, deltaY, maxVerticalSpeed);
                }
            }

            // Check for step violations
            if (deltaY > 0.6 && deltaY < 2.0 && horizontalDistance > 0.1) {
                double confidence = (deltaY - 0.6) / 1.4; // 0.6 is normal step height
                processCheck(player, "step", confidence, deltaY);
            }

        } catch (Exception e) {
            plugin.getWardenLogger().error("Error analyzing movement physics for " + player.getName(), e);
        }
    }

    /**
     * Analyze movement patterns for bot-like behavior
     */
    private void analyzeMovementPatterns(@NotNull Player player, @NotNull PlayerData playerData,
                                       @NotNull Location from, @NotNull Location to) {
        try {
            // Check for perfectly straight movement (bot-like)
            if (playerData.getLastLocation() != null) {
                double currentAngle = calculateMovementAngle(from, to);
                double lastAngle = calculateMovementAngle(playerData.getLastLocation(), from);

                double angleDifference = Math.abs(currentAngle - lastAngle);

                // Perfectly straight movement for extended periods is suspicious
                if (angleDifference < 0.1 && playerData.getMovementSpeed() > 0.1) {
                    double confidence = 1.0 - (angleDifference / 0.1);
                    processCheck(player, "bot_movement", confidence, angleDifference);
                }
            }

            // Check for impossible direction changes
            if (playerData.getLastVelocityX() != 0 || playerData.getLastVelocityZ() != 0) {
                double velocityChange = calculateVelocityChange(player, playerData);
                if (velocityChange > 2.0) { // Impossible instant direction change
                    double confidence = Math.min(velocityChange / 4.0, 1.0);
                    processCheck(player, "velocity_change", confidence, velocityChange);
                }
            }

        } catch (Exception e) {
            plugin.getWardenLogger().error("Error analyzing movement patterns for " + player.getName(), e);
        }
    }

    /**
     * Analyze environmental factors affecting movement
     */
    private void analyzeEnvironmentalFactors(@NotNull Player player, @NotNull PlayerData playerData,
                                           @NotNull Location from, @NotNull Location to) {
        try {
            // Check for Jesus/Water walking
            if (playerData.isInWater() && !player.isFlying()) {
                double deltaY = to.getY() - from.getY();
                if (deltaY >= 0 && playerData.getHorizontalSpeed() > 0.2) {
                    double confidence = Math.min(playerData.getHorizontalSpeed() / 0.5, 1.0);
                    processCheck(player, "jesus", confidence, playerData.getHorizontalSpeed());
                }
            }

            // Check for phase/no-clip through blocks
            if (isMovingThroughSolids(from, to)) {
                processCheck(player, "phase", 0.9, from, to);
            }

            // Check for spider climbing
            if (isClimbingWalls(from, to, player)) {
                double confidence = Math.abs(to.getY() - from.getY()) / 2.0;
                processCheck(player, "spider", confidence, to.getY() - from.getY());
            }

        } catch (Exception e) {
            plugin.getWardenLogger().error("Error analyzing environmental factors for " + player.getName(), e);
        }
    }

    /**
     * Calculate maximum allowed horizontal speed for a player
     */
    private double calculateMaxHorizontalSpeed(@NotNull Player player, @NotNull PlayerData playerData) {
        double baseSpeed = 4.3; // Normal walking speed (blocks per second)

        if (playerData.isSprinting()) {
            baseSpeed = 5.6; // Sprinting speed
        }

        if (playerData.isSneaking()) {
            baseSpeed *= 0.3; // Sneaking is much slower
        }

        if (playerData.isInWater()) {
            baseSpeed *= 0.3; // Swimming is slower
        }

        if (playerData.isInLava()) {
            baseSpeed *= 0.2; // Lava is even slower
        }

        if (playerData.isInWeb()) {
            baseSpeed *= 0.1; // Web is very slow
        }

        // Check for speed effects
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (effect.getType().equals(PotionEffectType.SPEED)) {
                baseSpeed *= (1.0 + (effect.getAmplifier() + 1) * 0.2);
            } else if (effect.getType().equals(PotionEffectType.SLOW)) {
                baseSpeed *= (1.0 - (effect.getAmplifier() + 1) * 0.15);
            }
        }

        // Add small tolerance for lag compensation
        return baseSpeed * 1.1;
    }

    /**
     * Calculate maximum allowed vertical speed for a player
     */
    private double calculateMaxVerticalSpeed(@NotNull Player player, @NotNull PlayerData playerData) {
        double baseSpeed = 0.42; // Normal jump height

        if (playerData.isInWater()) {
            baseSpeed = 0.2; // Swimming up
        }

        if (playerData.isInLava()) {
            baseSpeed = 0.15; // Lava swimming
        }

        // Check for jump boost
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (effect.getType().equals(PotionEffectType.JUMP)) {
                baseSpeed += (effect.getAmplifier() + 1) * 0.1;
            }
        }

        // Add tolerance for lag
        return baseSpeed * 1.2;
    }

    /**
     * Calculate movement angle between two locations
     */
    private double calculateMovementAngle(@NotNull Location from, @NotNull Location to) {
        double deltaX = to.getX() - from.getX();
        double deltaZ = to.getZ() - from.getZ();
        return Math.atan2(deltaZ, deltaX);
    }

    /**
     * Calculate velocity change magnitude
     */
    private double calculateVelocityChange(@NotNull Player player, @NotNull PlayerData playerData) {
        double currentVelX = player.getVelocity().getX();
        double currentVelZ = player.getVelocity().getZ();

        double lastVelX = playerData.getLastVelocityX();
        double lastVelZ = playerData.getLastVelocityZ();

        double deltaVelX = currentVelX - lastVelX;
        double deltaVelZ = currentVelZ - lastVelZ;

        return Math.sqrt(deltaVelX * deltaVelX + deltaVelZ * deltaVelZ);
    }

    /**
     * Check if player is moving through solid blocks
     */
    private boolean isMovingThroughSolids(@NotNull Location from, @NotNull Location to) {
        // Simple ray-casting to check for solid blocks between locations
        double distance = from.distance(to);
        if (distance < 0.1) return false;

        int steps = (int) Math.ceil(distance * 4); // 4 checks per block

        for (int i = 1; i < steps; i++) {
            double ratio = (double) i / steps;

            double x = from.getX() + (to.getX() - from.getX()) * ratio;
            double y = from.getY() + (to.getY() - from.getY()) * ratio;
            double z = from.getZ() + (to.getZ() - from.getZ()) * ratio;

            Location checkLoc = new Location(from.getWorld(), x, y, z);
            Block block = checkLoc.getBlock();

            if (block.getType().isSolid() && !isPassableBlock(block.getType())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if player is climbing walls (spider hack)
     */
    private boolean isClimbingWalls(@NotNull Location from, @NotNull Location to, @NotNull Player player) {
        double deltaY = to.getY() - from.getY();

        // Must be moving up
        if (deltaY <= 0) return false;

        // Check if there's a wall adjacent to the player
        Location playerLoc = player.getLocation();
        Block[] adjacentBlocks = {
            playerLoc.clone().add(1, 0, 0).getBlock(),
            playerLoc.clone().add(-1, 0, 0).getBlock(),
            playerLoc.clone().add(0, 0, 1).getBlock(),
            playerLoc.clone().add(0, 0, -1).getBlock()
        };

        boolean hasWall = false;
        for (Block block : adjacentBlocks) {
            if (block.getType().isSolid()) {
                hasWall = true;
                break;
            }
        }

        // Must have a wall and be moving up without jumping
        return hasWall && deltaY > 0.1 && !player.isOnGround();
    }

    /**
     * Check if a block type is passable (like air, water, etc.)
     */
    private boolean isPassableBlock(@NotNull Material material) {
        return material == Material.AIR ||
               material.name().contains("WATER") ||
               material.name().contains("LAVA") ||
               material.name().contains("SIGN") ||
               material.name().contains("TORCH") ||
               material.name().contains("FLOWER") ||
               material.name().contains("GRASS") ||
               material.name().contains("VINE");
    }
    
    /**
     * Clean up old player data
     */
    private void cleanupPlayerData() {
        long cutoffTime = System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5);
        
        playerDataMap.entrySet().removeIf(entry -> {
            PlayerData data = entry.getValue();
            if (data.getLastActivity() < cutoffTime) {
                data.cleanup();
                return true;
            }
            return false;
        });
    }
    
    /**
     * Reload detection configuration
     */
    public void reload() {
        plugin.getWardenLogger().info("Reloading detection system...");
        checkManager.reload();
        plugin.getWardenLogger().info("Detection system reloaded");
    }
    
    /**
     * Set low TPS mode
     */
    public void setLowTPSMode(boolean lowTPSMode) {
        if (this.lowTPSMode != lowTPSMode) {
            this.lowTPSMode = lowTPSMode;
            if (lowTPSMode) {
                plugin.getWardenLogger().warning("Entering low TPS mode - some checks disabled");
            } else {
                plugin.getWardenLogger().info("Exiting low TPS mode - all checks enabled");
            }
        }
    }
    
    /**
     * Enable or disable the detection system
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        plugin.getWardenLogger().info("Detection system " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Shutdown the detection system
     */
    public void shutdown() {
        plugin.getWardenLogger().info("Shutting down detection system...");
        
        enabled = false;
        
        // Shutdown executors
        detectionExecutor.shutdown();
        scheduledExecutor.shutdown();
        
        try {
            if (!detectionExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                detectionExecutor.shutdownNow();
            }
            if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            detectionExecutor.shutdownNow();
            scheduledExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // Clean up all player data
        for (PlayerData data : playerDataMap.values()) {
            data.cleanup();
        }
        playerDataMap.clear();
        
        plugin.getWardenLogger().info("Detection system shutdown complete");
    }
    
    // Getters
    public CheckManager getCheckManager() {
        return checkManager;
    }
    
    public boolean isLowTPSMode() {
        return lowTPSMode;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public int getPlayerDataCount() {
        return playerDataMap.size();
    }
}
