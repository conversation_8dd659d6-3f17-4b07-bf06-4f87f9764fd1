package com.vexleyofficial.warden.utils;

import org.bukkit.Bukkit;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;

/**
 * Utility class for checking server version compatibility and getting server information
 */
public class VersionChecker {
    
    private static final String[] SUPPORTED_VERSIONS = {
        "1.8", "1.9", "1.10", "1.11", "1.12", "1.13", "1.14", "1.15", 
        "1.16", "1.17", "1.18", "1.19", "1.20", "1.21"
    };
    
    private static String cachedVersion = null;
    private static Boolean cachedSupported = null;
    
    /**
     * Check if the current server version is supported
     */
    public static boolean isSupported() {
        if (cachedSupported != null) {
            return cachedSupported;
        }
        
        String version = getServerVersion();
        for (String supportedVersion : SUPPORTED_VERSIONS) {
            if (version.startsWith(supportedVersion)) {
                cachedSupported = true;
                return true;
            }
        }
        
        cachedSupported = false;
        return false;
    }
    
    /**
     * Get the server version string
     */
    @NotNull
    public static String getServerVersion() {
        if (cachedVersion != null) {
            return cachedVersion;
        }
        
        String version = Bukkit.getVersion();
        
        // Extract version number from strings like "git-Spigot-abc123 (MC: 1.20.4)"
        if (version.contains("MC: ")) {
            int start = version.indexOf("MC: ") + 4;
            int end = version.indexOf(")", start);
            if (end != -1) {
                cachedVersion = version.substring(start, end);
                return cachedVersion;
            }
        }
        
        // Fallback: try to extract from Bukkit version
        String bukkitVersion = Bukkit.getBukkitVersion();
        if (bukkitVersion.contains("-")) {
            cachedVersion = bukkitVersion.split("-")[0];
            return cachedVersion;
        }
        
        // Last resort: use full version string
        cachedVersion = version;
        return cachedVersion;
    }
    
    /**
     * Check if the server is running Paper
     */
    public static boolean isPaper() {
        try {
            Class.forName("com.destroystokyo.paper.PaperConfig");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Check if the server is running Spigot
     */
    public static boolean isSpigot() {
        try {
            Class.forName("org.spigotmc.SpigotConfig");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Get server TPS (Ticks Per Second)
     */
    public static double getTPS() {
        try {
            // Try Paper method first
            if (isPaper()) {
                return Bukkit.getTPS()[0];
            }
            
            // Try Spigot method
            if (isSpigot()) {
                Object server = Bukkit.getServer();
                Method getTPSMethod = server.getClass().getMethod("getTPS");
                double[] tps = (double[]) getTPSMethod.invoke(server);
                return tps[0];
            }
            
            // Fallback: estimate based on server tick time
            return estimateTPS();
            
        } catch (Exception e) {
            return estimateTPS();
        }
    }
    
    /**
     * Estimate TPS based on server performance
     */
    private static double estimateTPS() {
        // This is a rough estimation - in production you'd want a more accurate method
        return 20.0; // Assume 20 TPS if we can't measure it
    }
    
    /**
     * Check if the server version is at least the specified version
     */
    public static boolean isVersionAtLeast(@NotNull String minVersion) {
        String currentVersion = getServerVersion();
        return compareVersions(currentVersion, minVersion) >= 0;
    }
    
    /**
     * Compare two version strings
     * Returns: negative if v1 < v2, zero if v1 == v2, positive if v1 > v2
     */
    private static int compareVersions(@NotNull String v1, @NotNull String v2) {
        String[] parts1 = v1.split("\\.");
        String[] parts2 = v2.split("\\.");
        
        int maxLength = Math.max(parts1.length, parts2.length);
        
        for (int i = 0; i < maxLength; i++) {
            int num1 = i < parts1.length ? parseVersionPart(parts1[i]) : 0;
            int num2 = i < parts2.length ? parseVersionPart(parts2[i]) : 0;
            
            if (num1 != num2) {
                return Integer.compare(num1, num2);
            }
        }
        
        return 0;
    }
    
    /**
     * Parse a version part, handling non-numeric suffixes
     */
    private static int parseVersionPart(@NotNull String part) {
        StringBuilder numericPart = new StringBuilder();
        for (char c : part.toCharArray()) {
            if (Character.isDigit(c)) {
                numericPart.append(c);
            } else {
                break;
            }
        }
        
        try {
            return numericPart.length() > 0 ? Integer.parseInt(numericPart.toString()) : 0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * Get server software name
     */
    @NotNull
    public static String getServerSoftware() {
        if (isPaper()) {
            return "Paper";
        } else if (isSpigot()) {
            return "Spigot";
        } else {
            return "CraftBukkit";
        }
    }
    
    /**
     * Get detailed server information
     */
    @NotNull
    public static String getServerInfo() {
        return String.format("%s %s (Java %s)", 
            getServerSoftware(), 
            getServerVersion(), 
            System.getProperty("java.version"));
    }
}
