package com.vexleyofficial.warden.analytics;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

/**
 * Comprehensive analytics report for administrators
 */
public class AnalyticsReport {
    
    private long totalPlayers;
    private long highRiskPlayers;
    private Map<String, Long> cheatStatistics;
    private List<String> topCheats;
    private Map<String, Object> serverMetrics;
    private long reportTimestamp;
    
    public AnalyticsReport() {
        this.reportTimestamp = System.currentTimeMillis();
    }
    
    // Getters and setters
    public long getTotalPlayers() {
        return totalPlayers;
    }
    
    public void setTotalPlayers(long totalPlayers) {
        this.totalPlayers = totalPlayers;
    }
    
    public long getHighRiskPlayers() {
        return highRiskPlayers;
    }
    
    public void setHighRiskPlayers(long highRiskPlayers) {
        this.highRiskPlayers = highRiskPlayers;
    }
    
    public Map<String, Long> getCheatStatistics() {
        return cheatStatistics;
    }
    
    public void setCheatStatistics(Map<String, Long> cheatStatistics) {
        this.cheatStatistics = cheatStatistics;
    }
    
    public List<String> getTopCheats() {
        return topCheats;
    }
    
    public void setTopCheats(List<String> topCheats) {
        this.topCheats = topCheats;
    }
    
    public Map<String, Object> getServerMetrics() {
        return serverMetrics;
    }
    
    public void setServerMetrics(Map<String, Object> serverMetrics) {
        this.serverMetrics = serverMetrics;
    }
    
    public long getReportTimestamp() {
        return reportTimestamp;
    }
    
    /**
     * Generate formatted report string
     */
    public String generateFormattedReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== WARDEN ANTI-CHEAT ANALYTICS REPORT ===\n");
        report.append("Generated: ").append(new java.util.Date(reportTimestamp)).append("\n\n");
        
        // Player statistics
        report.append("PLAYER STATISTICS:\n");
        report.append("- Total Players Monitored: ").append(totalPlayers).append("\n");
        report.append("- High Risk Players: ").append(highRiskPlayers).append("\n");
        report.append("- Risk Percentage: ").append(String.format("%.1f%%", (highRiskPlayers * 100.0) / Math.max(totalPlayers, 1))).append("\n\n");
        
        // Cheat statistics
        report.append("CHEAT DETECTION STATISTICS:\n");
        if (cheatStatistics != null && !cheatStatistics.isEmpty()) {
            cheatStatistics.entrySet().stream()
                .sorted((a, b) -> Long.compare(b.getValue(), a.getValue()))
                .forEach(entry -> report.append("- ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" violations\n"));
        } else {
            report.append("- No violations detected\n");
        }
        report.append("\n");
        
        // Top cheats
        report.append("TOP DETECTED CHEATS:\n");
        if (topCheats != null && !topCheats.isEmpty()) {
            for (int i = 0; i < topCheats.size(); i++) {
                report.append((i + 1)).append(". ").append(topCheats.get(i)).append("\n");
            }
        } else {
            report.append("- No significant cheat patterns detected\n");
        }
        report.append("\n");
        
        // Server metrics
        report.append("SERVER PERFORMANCE:\n");
        if (serverMetrics != null) {
            report.append("- Current TPS: ").append(serverMetrics.get("current_tps")).append("\n");
            report.append("- Memory Usage: ").append(serverMetrics.get("current_memory_percent")).append("%\n");
            report.append("- CPU Usage: ").append(serverMetrics.get("current_cpu_percent")).append("%\n");
            report.append("- Performance Status: ").append(serverMetrics.get("performance_status")).append("\n");
        }
        
        report.append("\n=== END REPORT ===");
        
        return report.toString();
    }
}
