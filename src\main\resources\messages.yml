# Warden Anti-Cheat Messages
# Multi-language support

# English (en)
en:
  prefix: "&c[Warden] &f"
  
  # General messages
  no-permission: "You don't have permission to use this command."
  player-not-found: "Player '{player}' not found."
  invalid-command: "Invalid command. Use /warden help for assistance."
  
  # Commands
  help-header: "&cWarden Anti-Cheat Commands:"
  help-reload: "&7/warden reload &f- Reload configuration"
  help-info: "&7/warden info &f- Show plugin information"
  help-alerts: "&7/wardenalerts &f- Toggle alerts"
  help-check: "&7/warden check <player> &f- Check player status"
  
  # Alerts
  alerts-enabled: "Warden alerts enabled."
  alerts-disabled: "Warden alerts disabled."
  
  # Punishments
  kick-message: "&cYou have been kicked by Warden Anti-Cheat.\n&7Reason: {reason}\n&7If you believe this is an error, contact staff."
  ban-message: "&cYou have been banned by Warden Anti-Cheat.\n&7Reason: {reason}\n&7Appeal at: https://warden.anticheat/appeal"
  tempban-message: "&cYou have been temporarily banned by Warden Anti-Cheat.\n&7Reason: {reason}\n&7Duration: {duration}\n&7Appeal at: https://warden.anticheat/appeal"
  
  # Detection messages
  cheat-detected: "&c{player} &7flagged for &c{cheat} &7(Confidence: {confidence}%)"
  mitigation-applied: "&7Applied mitigation for &c{player}&7: {action}"
  
  # Info command
  info-header: "&cWarden Anti-Cheat Information:"
  info-version: "&7Version: &f{version}"
  info-author: "&7Author: &f{author}"
  info-cloud-status: "&7Cloud Status: {status}"
  info-players-monitored: "&7Players Monitored: &f{count}"
  
  # Reload command
  config-reloaded: "Configuration reloaded successfully."
  config-reload-error: "Error reloading configuration: {error}"

# Spanish (es)
es:
  prefix: "&c[Warden] &f"
  no-permission: "No tienes permisos para usar este comando."
  player-not-found: "Jugador '{player}' no encontrado."
  invalid-command: "Comando inválido. Usa /warden help para ayuda."
  alerts-enabled: "Alertas de Warden activadas."
  alerts-disabled: "Alertas de Warden desactivadas."
  kick-message: "&cHas sido expulsado por Warden Anti-Cheat.\n&7Razón: {reason}\n&7Si crees que es un error, contacta al staff."
  ban-message: "&cHas sido baneado por Warden Anti-Cheat.\n&7Razón: {reason}\n&7Apela en: https://warden.anticheat/appeal"
  cheat-detected: "&c{player} &7detectado por &c{cheat} &7(Confianza: {confidence}%)"

# French (fr)
fr:
  prefix: "&c[Warden] &f"
  no-permission: "Vous n'avez pas la permission d'utiliser cette commande."
  player-not-found: "Joueur '{player}' introuvable."
  invalid-command: "Commande invalide. Utilisez /warden help pour l'aide."
  alerts-enabled: "Alertes Warden activées."
  alerts-disabled: "Alertes Warden désactivées."
  kick-message: "&cVous avez été expulsé par Warden Anti-Cheat.\n&7Raison: {reason}\n&7Si vous pensez que c'est une erreur, contactez le staff."
  ban-message: "&cVous avez été banni par Warden Anti-Cheat.\n&7Raison: {reason}\n&7Faire appel: https://warden.anticheat/appeal"
  cheat-detected: "&c{player} &7signalé pour &c{cheat} &7(Confiance: {confidence}%)"

# German (de)
de:
  prefix: "&c[Warden] &f"
  no-permission: "Du hast keine Berechtigung, diesen Befehl zu verwenden."
  player-not-found: "Spieler '{player}' nicht gefunden."
  invalid-command: "Ungültiger Befehl. Verwende /warden help für Hilfe."
  alerts-enabled: "Warden-Benachrichtigungen aktiviert."
  alerts-disabled: "Warden-Benachrichtigungen deaktiviert."
  kick-message: "&cDu wurdest von Warden Anti-Cheat gekickt.\n&7Grund: {reason}\n&7Falls du denkst, dies ist ein Fehler, kontaktiere das Team."
  ban-message: "&cDu wurdest von Warden Anti-Cheat gebannt.\n&7Grund: {reason}\n&7Einspruch: https://warden.anticheat/appeal"
  cheat-detected: "&c{player} &7markiert für &c{cheat} &7(Vertrauen: {confidence}%)"
