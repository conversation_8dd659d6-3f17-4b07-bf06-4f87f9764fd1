package com.vexleyofficial.warden.monitoring;

import com.google.gson.Gson;
import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.analytics.AnalyticsReport;
import com.vexleyofficial.warden.analytics.PlayerProfile;
import okhttp3.*;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Advanced monitoring and alerting system for real-time threat detection
 */
public class MonitoringSystem {
    
    private final WardenAntiCheat plugin;
    private final ScheduledExecutorService monitoringExecutor;
    private final OkHttpClient httpClient;
    private final Gson gson;
    
    // Alert thresholds
    private final Map<String, AlertThreshold> alertThresholds;
    private final Map<String, Long> lastAlertTimes;
    private final Set<String> activeAlerts;
    
    // Monitoring metrics
    private final Map<String, Object> currentMetrics;
    private final Queue<MonitoringEvent> eventHistory;
    
    public MonitoringSystem(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.monitoringExecutor = Executors.newScheduledThreadPool(3);
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .build();
        this.gson = new Gson();
        
        this.alertThresholds = new ConcurrentHashMap<>();
        this.lastAlertTimes = new ConcurrentHashMap<>();
        this.activeAlerts = ConcurrentHashMap.newKeySet();
        this.currentMetrics = new ConcurrentHashMap<>();
        this.eventHistory = new LinkedList<>();
        
        initializeAlertThresholds();
    }
    
    /**
     * Initialize the monitoring system
     */
    public void initialize() {
        plugin.getWardenLogger().info("Initializing advanced monitoring system...");
        
        // Start monitoring tasks
        startThreatMonitoring();
        startPerformanceMonitoring();
        startHealthChecks();
        startReporting();
        
        plugin.getWardenLogger().info("Monitoring system initialized successfully");
    }
    
    /**
     * Initialize default alert thresholds
     */
    private void initializeAlertThresholds() {
        // Cheat detection thresholds
        alertThresholds.put("high_risk_player", new AlertThreshold(0.8, 300000)); // 80% risk, 5min cooldown
        alertThresholds.put("violation_spike", new AlertThreshold(10, 600000)); // 10 violations, 10min cooldown
        alertThresholds.put("new_cheat_pattern", new AlertThreshold(5, 1800000)); // 5 instances, 30min cooldown
        
        // Performance thresholds
        alertThresholds.put("low_tps", new AlertThreshold(15.0, 300000)); // TPS < 15, 5min cooldown
        alertThresholds.put("high_memory", new AlertThreshold(85.0, 600000)); // Memory > 85%, 10min cooldown
        alertThresholds.put("high_cpu", new AlertThreshold(90.0, 300000)); // CPU > 90%, 5min cooldown
        
        // Security thresholds
        alertThresholds.put("mass_join", new AlertThreshold(20, 60000)); // 20 joins in 1min
        alertThresholds.put("coordinated_attack", new AlertThreshold(5, 300000)); // 5 coordinated cheaters
    }
    
    /**
     * Start threat monitoring
     */
    private void startThreatMonitoring() {
        monitoringExecutor.scheduleAtFixedRate(() -> {
            try {
                monitorHighRiskPlayers();
                monitorViolationSpikes();
                monitorCoordinatedAttacks();
                monitorNewCheatPatterns();
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error in threat monitoring", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * Start performance monitoring
     */
    private void startPerformanceMonitoring() {
        monitoringExecutor.scheduleAtFixedRate(() -> {
            try {
                monitorServerPerformance();
                monitorPluginHealth();
                monitorCloudConnectivity();
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error in performance monitoring", e);
            }
        }, 0, 60, TimeUnit.SECONDS);
    }
    
    /**
     * Start health checks
     */
    private void startHealthChecks() {
        monitoringExecutor.scheduleAtFixedRate(() -> {
            try {
                performHealthCheck();
                checkSystemIntegrity();
                validateConfiguration();
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error in health checks", e);
            }
        }, 0, 300, TimeUnit.SECONDS); // Every 5 minutes
    }
    
    /**
     * Start periodic reporting
     */
    private void startReporting() {
        monitoringExecutor.scheduleAtFixedRate(() -> {
            try {
                generateAndSendReport();
                cleanupOldEvents();
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error in reporting", e);
            }
        }, 0, 3600, TimeUnit.SECONDS); // Every hour
    }
    
    /**
     * Monitor high-risk players
     */
    private void monitorHighRiskPlayers() {
        if (!plugin.getDetectionManager().isEnabled()) return;
        
        Map<UUID, PlayerProfile> profiles = plugin.getAnalyticsEngine().getPlayerProfiles();
        
        for (PlayerProfile profile : profiles.values()) {
            if (profile.getRiskScore() > alertThresholds.get("high_risk_player").getValue()) {
                triggerAlert("HIGH_RISK_PLAYER", 
                    "Player " + profile.getPlayerName() + " has risk score: " + 
                    String.format("%.2f", profile.getRiskScore()),
                    AlertSeverity.HIGH);
            }
        }
    }
    
    /**
     * Monitor for violation spikes
     */
    private void monitorViolationSpikes() {
        // Count recent violations across all players
        long recentViolations = plugin.getAnalyticsEngine().getCheatStatistics().values().stream()
            .mapToLong(stats -> stats.getRecentViolations())
            .sum();
        
        if (recentViolations > alertThresholds.get("violation_spike").getValue()) {
            triggerAlert("VIOLATION_SPIKE",
                "Detected " + recentViolations + " violations in the last hour",
                AlertSeverity.HIGH);
        }
    }
    
    /**
     * Monitor for coordinated attacks
     */
    private void monitorCoordinatedAttacks() {
        // Check for multiple players with similar violation patterns
        Map<UUID, PlayerProfile> profiles = plugin.getAnalyticsEngine().getPlayerProfiles();
        
        long highRiskCount = profiles.values().stream()
            .mapToLong(profile -> profile.getRiskScore() > 0.6 ? 1 : 0)
            .sum();
        
        if (highRiskCount >= alertThresholds.get("coordinated_attack").getValue()) {
            triggerAlert("COORDINATED_ATTACK",
                "Detected " + highRiskCount + " high-risk players simultaneously",
                AlertSeverity.CRITICAL);
        }
    }
    
    /**
     * Monitor for new cheat patterns
     */
    private void monitorNewCheatPatterns() {
        // This would analyze for new, unknown cheat signatures
        // For now, we'll check for unusual cheat combinations
        
        plugin.getAnalyticsEngine().getCheatStatistics().entrySet().stream()
            .filter(entry -> entry.getValue().isTrendingUp())
            .filter(entry -> entry.getValue().getRecentViolations() > 5)
            .forEach(entry -> {
                triggerAlert("NEW_CHEAT_PATTERN",
                    "Trending cheat detected: " + entry.getKey() + 
                    " (" + entry.getValue().getRecentViolations() + " recent violations)",
                    AlertSeverity.MEDIUM);
            });
    }
    
    /**
     * Monitor server performance
     */
    private void monitorServerPerformance() {
        var serverMetrics = plugin.getAnalyticsEngine().getServerMetrics();
        
        // Check TPS
        if (serverMetrics.getCurrentTPS() < alertThresholds.get("low_tps").getValue()) {
            triggerAlert("LOW_TPS",
                "Server TPS dropped to " + String.format("%.1f", serverMetrics.getCurrentTPS()),
                AlertSeverity.HIGH);
        }
        
        // Check memory usage
        if (serverMetrics.getCurrentMemoryUsagePercent() > alertThresholds.get("high_memory").getValue()) {
            triggerAlert("HIGH_MEMORY",
                "Memory usage at " + String.format("%.1f%%", serverMetrics.getCurrentMemoryUsagePercent()),
                AlertSeverity.MEDIUM);
        }
        
        // Check CPU usage
        if (serverMetrics.getCurrentCPUUsage() * 100 > alertThresholds.get("high_cpu").getValue()) {
            triggerAlert("HIGH_CPU",
                "CPU usage at " + String.format("%.1f%%", serverMetrics.getCurrentCPUUsage() * 100),
                AlertSeverity.MEDIUM);
        }
    }
    
    /**
     * Monitor plugin health
     */
    private void monitorPluginHealth() {
        // Check if detection system is running
        if (!plugin.getDetectionManager().isEnabled()) {
            triggerAlert("DETECTION_DISABLED",
                "Detection system is disabled",
                AlertSeverity.CRITICAL);
        }
        
        // Check cloud connectivity
        if (plugin.getConfigManager().isCloudEnabled() && !plugin.isCloudConnected()) {
            triggerAlert("CLOUD_DISCONNECTED",
                "Lost connection to Warden Cloud",
                AlertSeverity.MEDIUM);
        }
        
        // Check database connectivity
        if (!plugin.getDatabaseManager().isInitialized()) {
            triggerAlert("DATABASE_ERROR",
                "Database connection lost",
                AlertSeverity.HIGH);
        }
    }
    
    /**
     * Monitor cloud connectivity
     */
    private void monitorCloudConnectivity() {
        if (!plugin.getConfigManager().isCloudEnabled()) return;
        
        boolean connected = plugin.getCloudCommunicator().isConnected();
        currentMetrics.put("cloud_connected", connected);
        
        if (!connected) {
            triggerAlert("CLOUD_CONNECTIVITY",
                "Cloud connection is unstable",
                AlertSeverity.MEDIUM);
        }
    }
    
    /**
     * Perform comprehensive health check
     */
    private void performHealthCheck() {
        Map<String, Object> healthStatus = new HashMap<>();
        
        // Check core components
        healthStatus.put("detection_manager", plugin.getDetectionManager().isEnabled());
        healthStatus.put("cloud_communicator", plugin.isCloudConnected());
        healthStatus.put("database_manager", plugin.getDatabaseManager().isInitialized());
        healthStatus.put("analytics_engine", plugin.getAnalyticsEngine() != null);
        
        // Check configuration
        healthStatus.put("api_key_configured", plugin.getConfigManager().isApiKeyConfigured());
        healthStatus.put("real_time_mitigation", plugin.getConfigManager().isRealTimeMitigation());
        
        currentMetrics.put("health_status", healthStatus);
        
        // Log health status
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Health check completed: " + healthStatus);
        }
    }
    
    /**
     * Check system integrity
     */
    private void checkSystemIntegrity() {
        // Verify critical files exist
        // Check for tampering
        // Validate checksums
        // This would be implemented based on specific security requirements
    }
    
    /**
     * Validate configuration
     */
    private void validateConfiguration() {
        List<String> issues = new ArrayList<>();
        
        if (!plugin.getConfigManager().isApiKeyConfigured()) {
            issues.add("API key not configured");
        }
        
        if (plugin.getConfigManager().getMinConfidence() < 50) {
            issues.add("Minimum confidence threshold is very low");
        }
        
        if (!issues.isEmpty()) {
            triggerAlert("CONFIGURATION_ISSUES",
                "Configuration issues detected: " + String.join(", ", issues),
                AlertSeverity.LOW);
        }
    }
    
    /**
     * Trigger an alert with cooldown
     */
    private void triggerAlert(@NotNull String alertType, @NotNull String message, @NotNull AlertSeverity severity) {
        AlertThreshold threshold = alertThresholds.get(alertType.toLowerCase());
        if (threshold == null) return;
        
        long currentTime = System.currentTimeMillis();
        Long lastAlert = lastAlertTimes.get(alertType);
        
        // Check cooldown
        if (lastAlert != null && currentTime - lastAlert < threshold.getCooldownMs()) {
            return;
        }
        
        // Record alert
        lastAlertTimes.put(alertType, currentTime);
        activeAlerts.add(alertType);
        
        // Create monitoring event
        MonitoringEvent event = new MonitoringEvent(alertType, message, severity, currentTime);
        eventHistory.offer(event);
        
        // Keep only recent events
        while (eventHistory.size() > 1000) {
            eventHistory.poll();
        }
        
        // Send alert
        sendAlert(event);
        
        // Log alert
        plugin.getWardenLogger().warning("ALERT [" + severity + "] " + alertType + ": " + message);
    }
    
    /**
     * Send alert through configured channels
     */
    private void sendAlert(@NotNull MonitoringEvent event) {
        // Send to Discord webhook if configured
        String discordWebhook = plugin.getConfigManager().getDiscordWebhook();
        if (!discordWebhook.isEmpty()) {
            sendDiscordAlert(discordWebhook, event);
        }
        
        // Send to online staff
        sendStaffAlert(event);
        
        // Send to external monitoring systems
        sendExternalAlert(event);
    }
    
    /**
     * Send Discord webhook alert
     */
    private void sendDiscordAlert(@NotNull String webhookUrl, @NotNull MonitoringEvent event) {
        try {
            Map<String, Object> payload = new HashMap<>();
            payload.put("content", "🚨 **Warden Alert**");
            
            Map<String, Object> embed = new HashMap<>();
            embed.put("title", event.getAlertType());
            embed.put("description", event.getMessage());
            embed.put("color", event.getSeverity().getColor());
            embed.put("timestamp", new Date(event.getTimestamp()).toInstant().toString());
            
            payload.put("embeds", Collections.singletonList(embed));
            
            RequestBody body = RequestBody.create(gson.toJson(payload), MediaType.get("application/json"));
            Request request = new Request.Builder()
                .url(webhookUrl)
                .post(body)
                .build();
            
            httpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    plugin.getWardenLogger().warning("Failed to send Discord alert: " + e.getMessage());
                }
                
                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) {
                    response.close();
                }
            });
            
        } catch (Exception e) {
            plugin.getWardenLogger().error("Error sending Discord alert", e);
        }
    }
    
    /**
     * Send alert to online staff
     */
    private void sendStaffAlert(@NotNull MonitoringEvent event) {
        String alertMessage = "§c[WARDEN ALERT] §f" + event.getAlertType() + ": " + event.getMessage();
        
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.hasPermission("warden.alerts")) {
                player.sendMessage(alertMessage);
            }
        }
    }
    
    /**
     * Send alert to external monitoring systems
     */
    private void sendExternalAlert(@NotNull MonitoringEvent event) {
        // This would integrate with external monitoring systems like:
        // - Prometheus/Grafana
        // - Datadog
        // - New Relic
        // - Custom monitoring APIs
    }
    
    /**
     * Generate and send periodic report
     */
    private void generateAndSendReport() {
        AnalyticsReport report = plugin.getAnalyticsEngine().generateAnalyticsReport();
        
        // Send to configured channels
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().info("Generated analytics report: " + report.getTotalPlayers() + " players monitored");
        }
        
        // This could be extended to send reports via email, webhooks, etc.
    }
    
    /**
     * Clean up old monitoring events
     */
    private void cleanupOldEvents() {
        long cutoffTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7);
        eventHistory.removeIf(event -> event.getTimestamp() < cutoffTime);
        
        // Clear resolved alerts
        activeAlerts.removeIf(alertType -> {
            Long lastAlert = lastAlertTimes.get(alertType);
            return lastAlert != null && System.currentTimeMillis() - lastAlert > TimeUnit.HOURS.toMillis(1);
        });
    }
    
    /**
     * Shutdown the monitoring system
     */
    public void shutdown() {
        monitoringExecutor.shutdown();
        try {
            if (!monitoringExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                monitoringExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            monitoringExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
        
        plugin.getWardenLogger().info("Monitoring system shutdown complete");
    }
    
    // Getters
    public Set<String> getActiveAlerts() {
        return new HashSet<>(activeAlerts);
    }
    
    public Map<String, Object> getCurrentMetrics() {
        return new HashMap<>(currentMetrics);
    }
    
    public List<MonitoringEvent> getRecentEvents(int limit) {
        return eventHistory.stream()
            .sorted((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()))
            .limit(limit)
            .toList();
    }
}
