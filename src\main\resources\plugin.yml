name: WardenAC
version: '${version}'
description: '${description}'
main: com.vexleyofficial.warden.WardenAntiCheat
author: vexleyofficial
website: https://warden.anticheat
api-version: 1.13

softdepend:
  - ProtocolLib
  - ViaVersion
  - PlaceholderAPI

commands:
  warden:
    description: Main Warden Anti-Cheat command
    usage: /warden <subcommand>
    permission: warden.admin
    aliases: [wac, anticheat]
  
  wardenalerts:
    description: Toggle Warden alerts
    usage: /wardenalerts
    permission: warden.alerts
    aliases: [walerts]

permissions:
  warden.*:
    description: All Warden permissions
    default: op
    children:
      warden.admin: true
      warden.alerts: true
      warden.bypass: true
      warden.notify: true
  
  warden.admin:
    description: Administrative access to Warden
    default: op
  
  warden.alerts:
    description: Receive Warden alerts
    default: op
  
  warden.bypass:
    description: Bypass all Warden checks
    default: false
  
  warden.notify:
    description: Receive notifications about punishments
    default: op
