package com.vexleyofficial.warden.mitigation;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.cloud.models.DetectionResponse;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

/**
 * Handles real-time mitigation of detected cheats
 */
public class MitigationManager {
    
    private final WardenAntiCheat plugin;
    
    public MitigationManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Initialize the mitigation system
     */
    public void initialize() {
        plugin.getWardenLogger().info("Mitigation system initialized");
    }
    
    /**
     * Apply mitigation based on cloud response
     */
    public void applyMitigation(@NotNull Player player, @NotNull DetectionResponse response) {
        if (!plugin.getConfigManager().isRealTimeMitigation()) {
            return;
        }
        
        if (response.getMitigationActions() == null) {
            return;
        }
        
        // Sort mitigation actions by priority
        response.getMitigationActions().stream()
            .sorted((a, b) -> Integer.compare(b.getPriority(), a.getPriority()))
            .forEach(action -> applyMitigationAction(player, action));
    }
    
    /**
     * Apply a specific mitigation action
     */
    private void applyMitigationAction(@NotNull Player player, @NotNull DetectionResponse.MitigationAction action) {
        try {
            switch (action.getType().toLowerCase()) {
                case "teleport_back":
                    applyTeleportBack(player, action.getParameters());
                    break;
                    
                case "apply_gravity":
                    applyGravity(player, action.getParameters());
                    break;
                    
                case "limit_reach":
                    limitReach(player, action.getParameters());
                    break;
                    
                case "normalize_velocity":
                    normalizeVelocity(player, action.getParameters());
                    break;
                    
                case "apply_fall_damage":
                    applyFallDamage(player, action.getParameters());
                    break;
                    
                case "limit_cps":
                    limitCPS(player, action.getParameters());
                    break;
                    
                case "cancel_action":
                    cancelAction(player, action.getParameters());
                    break;
                    
                default:
                    plugin.getWardenLogger().warning("Unknown mitigation action: " + action.getType());
                    break;
            }
            
            if (plugin.getConfigManager().isDebugMode()) {
                plugin.getWardenLogger().debug("Applied mitigation '" + action.getType() + "' to " + player.getName());
            }
            
        } catch (Exception e) {
            plugin.getWardenLogger().error("Error applying mitigation action " + action.getType() + " to " + player.getName(), e);
        }
    }
    
    /**
     * Teleport player back to their last valid location
     */
    private void applyTeleportBack(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        Bukkit.getScheduler().runTask(plugin, () -> {
            Location lastLocation = plugin.getDetectionManager().getPlayerData(player).getLastLocation();
            if (lastLocation != null) {
                player.teleport(lastLocation);
                
                if (plugin.getConfigManager().isDebugMode()) {
                    plugin.getWardenLogger().debug("Teleported " + player.getName() + " back to last valid location");
                }
            }
        });
    }
    
    /**
     * Apply gravity to flying player
     */
    private void applyGravity(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        Bukkit.getScheduler().runTask(plugin, () -> {
            double force = getDoubleParameter(parameters, "force", 0.5);
            Vector velocity = player.getVelocity();
            velocity.setY(-force);
            player.setVelocity(velocity);
            
            if (plugin.getConfigManager().isDebugMode()) {
                plugin.getWardenLogger().debug("Applied gravity to " + player.getName() + " with force " + force);
            }
        });
    }
    
    /**
     * Limit player reach by canceling the action
     */
    private void limitReach(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        // This would typically be handled in the combat listener
        // by canceling the attack event if reach exceeds limits
        double maxReach = getDoubleParameter(parameters, "max_reach", 3.0);
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Limited reach for " + player.getName() + " to " + maxReach + " blocks");
        }
    }
    
    /**
     * Normalize player velocity to prevent speed hacks
     */
    private void normalizeVelocity(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        Bukkit.getScheduler().runTask(plugin, () -> {
            double maxSpeed = getDoubleParameter(parameters, "max_speed", 0.5);
            Vector velocity = player.getVelocity();
            
            // Normalize horizontal velocity
            double horizontalSpeed = Math.sqrt(velocity.getX() * velocity.getX() + velocity.getZ() * velocity.getZ());
            if (horizontalSpeed > maxSpeed) {
                double factor = maxSpeed / horizontalSpeed;
                velocity.setX(velocity.getX() * factor);
                velocity.setZ(velocity.getZ() * factor);
                player.setVelocity(velocity);
                
                if (plugin.getConfigManager().isDebugMode()) {
                    plugin.getWardenLogger().debug("Normalized velocity for " + player.getName() + " to max speed " + maxSpeed);
                }
            }
        });
    }
    
    /**
     * Apply fall damage to player who avoided it
     */
    private void applyFallDamage(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        Bukkit.getScheduler().runTask(plugin, () -> {
            double damage = getDoubleParameter(parameters, "damage", 4.0);
            player.damage(damage);
            
            if (plugin.getConfigManager().isDebugMode()) {
                plugin.getWardenLogger().debug("Applied " + damage + " fall damage to " + player.getName());
            }
        });
    }
    
    /**
     * Limit clicks per second by adding delay
     */
    private void limitCPS(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        // This would typically be handled by tracking click times
        // and ignoring clicks that exceed the CPS limit
        int maxCPS = getIntParameter(parameters, "max_cps", 15);
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Limited CPS for " + player.getName() + " to " + maxCPS);
        }
    }
    
    /**
     * Cancel the current action
     */
    private void cancelAction(@NotNull Player player, @NotNull Map<String, Object> parameters) {
        // This would typically be handled in the event listeners
        // by canceling the specific event that triggered the detection
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Canceled action for " + player.getName());
        }
    }
    
    /**
     * Apply direct mitigation for specific cheat types
     */
    public void applyDirectMitigation(@NotNull Player player, @NotNull String cheatType, double confidence) {
        if (!plugin.getConfigManager().isRealTimeMitigation()) {
            return;
        }
        
        switch (cheatType.toLowerCase()) {
            case "fly":
                applyGravity(player, Map.of("force", 0.8));
                break;
                
            case "speed":
                normalizeVelocity(player, Map.of("max_speed", 0.3));
                break;
                
            case "reach":
                limitReach(player, Map.of("max_reach", 3.0));
                break;
                
            case "nofall":
                applyFallDamage(player, Map.of("damage", confidence * 10));
                break;
                
            case "autoclicker":
                limitCPS(player, Map.of("max_cps", 12));
                break;
                
            default:
                // Generic mitigation - teleport back
                applyTeleportBack(player, Map.of());
                break;
        }
    }
    
    /**
     * Get double parameter from map with default value
     */
    private double getDoubleParameter(@NotNull Map<String, Object> parameters, @NotNull String key, double defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }
    
    /**
     * Get integer parameter from map with default value
     */
    private int getIntParameter(@NotNull Map<String, Object> parameters, @NotNull String key, int defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
}
