package com.vexleyofficial.warden.detection.checks;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.inventory.ItemStack;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * World interaction detection checks (FastBreak, FastPlace, X-Ray, etc.)
 */
public class WorldChecks {
    
    private final WardenAntiCheat plugin;
    private final Map<Player, List<Long>> breakTimes = new HashMap<>();
    private final Map<Player, List<Long>> placeTimes = new HashMap<>();
    private final Map<Player, List<Material>> recentlyMinedOres = new HashMap<>();
    
    public WorldChecks(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    /**
     * FastBreak detection
     */
    public void checkFastBreak(@NotNull Player player, @NotNull BlockBreakEvent event, @NotNull PlayerData playerData) {
        Block block = event.getBlock();
        Material blockType = block.getType();
        
        // Calculate expected break time
        double expectedBreakTime = calculateBreakTime(blockType, player);
        
        // Track break times
        long currentTime = System.currentTimeMillis();
        List<Long> times = breakTimes.computeIfAbsent(player, k -> new ArrayList<>());
        times.add(currentTime);
        
        // Keep only recent break times (last 5 seconds)
        times.removeIf(time -> currentTime - time > 5000);
        
        // Check break frequency
        if (times.size() > 1) {
            long lastBreakTime = times.get(times.size() - 2);
            long timeDiff = currentTime - lastBreakTime;
            
            if (timeDiff < expectedBreakTime * 0.5) { // Breaking too fast
                double confidence = 1.0 - (timeDiff / (expectedBreakTime * 0.5));
                playerData.addViolation("fastbreak", confidence);
                
                plugin.getDetectionManager().processCheck(player, "fastbreak", confidence, timeDiff, expectedBreakTime);
                
                // Cancel the event if confidence is high
                if (confidence > 0.8) {
                    event.setCancelled(true);
                }
            }
        }
        
        // Check for nuker (breaking many blocks quickly)
        checkNuker(player, playerData, times);
        
        // Track ore mining for X-Ray detection
        if (isOre(blockType)) {
            trackOreMining(player, blockType);
        }
    }
    
    /**
     * FastPlace detection
     */
    public void checkFastPlace(@NotNull Player player, @NotNull BlockPlaceEvent event, @NotNull PlayerData playerData) {
        long currentTime = System.currentTimeMillis();
        List<Long> times = placeTimes.computeIfAbsent(player, k -> new ArrayList<>());
        times.add(currentTime);
        
        // Keep only recent place times (last 5 seconds)
        times.removeIf(time -> currentTime - time > 5000);
        
        // Check place frequency
        if (times.size() > 1) {
            long lastPlaceTime = times.get(times.size() - 2);
            long timeDiff = currentTime - lastPlaceTime;
            
            // Minimum time between placements should be around 50ms
            if (timeDiff < 50) {
                double confidence = 1.0 - (timeDiff / 50.0);
                playerData.addViolation("fastplace", confidence);
                
                plugin.getDetectionManager().processCheck(player, "fastplace", confidence, timeDiff);
                
                // Cancel if too fast
                if (confidence > 0.8) {
                    event.setCancelled(true);
                }
            }
        }
        
        // Check for scaffold (rapid bridge building)
        checkScaffold(player, event, playerData);
    }
    
    /**
     * Nuker detection (breaking many blocks rapidly)
     */
    private void checkNuker(@NotNull Player player, @NotNull PlayerData playerData, @NotNull List<Long> breakTimes) {
        // If breaking more than 10 blocks in 2 seconds
        long twoSecondsAgo = System.currentTimeMillis() - 2000;
        long recentBreaks = breakTimes.stream().mapToLong(time -> time > twoSecondsAgo ? 1 : 0).sum();
        
        if (recentBreaks > 10) {
            double confidence = Math.min(recentBreaks / 20.0, 1.0);
            playerData.addViolation("nuker", confidence);
            
            plugin.getDetectionManager().processCheck(player, "nuker", confidence, recentBreaks);
        }
    }
    
    /**
     * Scaffold detection (rapid bridge building)
     */
    private void checkScaffold(@NotNull Player player, @NotNull BlockPlaceEvent event, @NotNull PlayerData playerData) {
        Block placedBlock = event.getBlock();
        Location playerLoc = player.getLocation();
        Location blockLoc = placedBlock.getLocation();
        
        // Check if placing blocks while moving (scaffold characteristic)
        double horizontalSpeed = playerData.getHorizontalSpeed();
        double distanceToBlock = playerLoc.distance(blockLoc);
        
        if (horizontalSpeed > 2.0 && distanceToBlock > 2.5) {
            double confidence = Math.min(horizontalSpeed / 4.0, 1.0);
            playerData.addViolation("scaffold", confidence);
            
            plugin.getDetectionManager().processCheck(player, "scaffold", confidence, horizontalSpeed, distanceToBlock);
        }
        
        // Check for placing blocks below feet while moving
        if (blockLoc.getY() < playerLoc.getY() && horizontalSpeed > 1.0) {
            double confidence = Math.min(horizontalSpeed / 3.0, 1.0);
            playerData.addViolation("scaffold", confidence);
            
            plugin.getDetectionManager().processCheck(player, "scaffold", confidence, "below_feet");
        }
    }
    
    /**
     * X-Ray detection through ore mining patterns
     */
    public void checkXRay(@NotNull Player player, @NotNull PlayerData playerData) {
        List<Material> ores = recentlyMinedOres.get(player);
        if (ores == null || ores.size() < 10) return;
        
        // Calculate ore ratio (valuable ores vs total blocks mined)
        long valuableOres = ores.stream().mapToLong(ore -> isValuableOre(ore) ? 1 : 0).sum();
        double oreRatio = (double) valuableOres / ores.size();
        
        // Normal mining should have lower ore ratios
        if (oreRatio > 0.6) { // 60% valuable ores is suspicious
            double confidence = Math.min(oreRatio / 0.8, 1.0);
            playerData.addViolation("xray", confidence);
            
            plugin.getDetectionManager().processCheck(player, "xray", confidence, oreRatio, valuableOres, ores.size());
        }
        
        // Check for mining straight to ores
        checkDirectOreMining(player, playerData, ores);
    }
    
    /**
     * Check for mining directly to ores (X-Ray indicator)
     */
    private void checkDirectOreMining(@NotNull Player player, @NotNull PlayerData playerData, @NotNull List<Material> ores) {
        // This would require tracking mining paths
        // For now, we'll implement a basic version
        
        // Count consecutive valuable ore finds
        int consecutiveOres = 0;
        int maxConsecutive = 0;
        
        for (Material ore : ores) {
            if (isValuableOre(ore)) {
                consecutiveOres++;
                maxConsecutive = Math.max(maxConsecutive, consecutiveOres);
            } else {
                consecutiveOres = 0;
            }
        }
        
        // Finding 5+ valuable ores in a row is suspicious
        if (maxConsecutive >= 5) {
            double confidence = Math.min(maxConsecutive / 8.0, 1.0);
            playerData.addViolation("xray_consecutive", confidence);
            
            plugin.getDetectionManager().processCheck(player, "xray", confidence, "consecutive", maxConsecutive);
        }
    }
    
    /**
     * Freecam detection (looking through walls)
     */
    public void checkFreecam(@NotNull Player player, @NotNull PlayerData playerData) {
        Location eyeLoc = player.getEyeLocation();
        
        // Check if player is looking at blocks through walls
        // This would require ray-tracing to see if there are solid blocks between
        // the player and what they're looking at
        
        // For now, we'll implement a basic version that checks for
        // interactions with blocks that shouldn't be visible
        
        // This would be triggered by other events like block interaction
        // when the player shouldn't be able to see the block
    }
    
    /**
     * Chest ESP detection (finding chests too easily)
     */
    public void checkChestESP(@NotNull Player player, @NotNull PlayerData playerData, @NotNull Block interactedBlock) {
        if (!isContainer(interactedBlock.getType())) return;
        
        Location playerLoc = player.getLocation();
        Location chestLoc = interactedBlock.getLocation();
        
        // Check if player went directly to chest without exploring
        double distance = playerLoc.distance(chestLoc);
        
        // If player found chest from far away without exploring nearby area
        if (distance > 10) {
            // Check if player has been near this area recently
            // This would require tracking player movement history
            
            double confidence = Math.min(distance / 20.0, 0.8);
            playerData.addViolation("chest_esp", confidence);
            
            plugin.getDetectionManager().processCheck(player, "chest_esp", confidence, distance);
        }
    }
    
    /**
     * Track ore mining for X-Ray analysis
     */
    private void trackOreMining(@NotNull Player player, @NotNull Material ore) {
        List<Material> ores = recentlyMinedOres.computeIfAbsent(player, k -> new ArrayList<>());
        ores.add(ore);
        
        // Keep only recent mining (last 50 blocks)
        if (ores.size() > 50) {
            ores.remove(0);
        }
    }
    
    /**
     * Calculate expected break time for a block
     */
    private double calculateBreakTime(@NotNull Material blockType, @NotNull Player player) {
        // Base break times (in milliseconds)
        double baseTime = switch (blockType) {
            case STONE -> 1500;
            case COBBLESTONE -> 1500;
            case DIRT -> 500;
            case GRASS_BLOCK -> 600;
            case SAND -> 500;
            case GRAVEL -> 600;
            case WOOD, OAK_LOG -> 2000;
            case DIAMOND_ORE -> 3750;
            case IRON_ORE -> 3000;
            case COAL_ORE -> 3000;
            case OBSIDIAN -> 37500; // Very long
            default -> 1000;
        };
        
        // Adjust for tool efficiency
        ItemStack tool = player.getInventory().getItemInMainHand();
        if (tool != null && tool.getType().name().contains("PICKAXE")) {
            baseTime *= 0.5; // Pickaxe makes mining faster
        }
        
        return baseTime;
    }
    
    /**
     * Check if material is an ore
     */
    private boolean isOre(@NotNull Material material) {
        return material.name().contains("_ORE") || 
               material == Material.ANCIENT_DEBRIS ||
               material == Material.NETHER_QUARTZ_ORE;
    }
    
    /**
     * Check if material is a valuable ore
     */
    private boolean isValuableOre(@NotNull Material material) {
        return material == Material.DIAMOND_ORE ||
               material == Material.EMERALD_ORE ||
               material == Material.GOLD_ORE ||
               material == Material.ANCIENT_DEBRIS ||
               material == Material.NETHER_GOLD_ORE;
    }
    
    /**
     * Check if material is a container
     */
    private boolean isContainer(@NotNull Material material) {
        return material == Material.CHEST ||
               material == Material.TRAPPED_CHEST ||
               material == Material.ENDER_CHEST ||
               material == Material.SHULKER_BOX ||
               material.name().contains("SHULKER_BOX");
    }
    
    /**
     * Clean up player data when they leave
     */
    public void cleanupPlayer(@NotNull Player player) {
        breakTimes.remove(player);
        placeTimes.remove(player);
        recentlyMinedOres.remove(player);
    }
}
