package com.vexleyofficial.warden.listeners;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.jetbrains.annotations.NotNull;

/**
 * Handles player join/quit events for data management
 */
public class PlayerListener implements Listener {
    
    private final WardenAntiCheat plugin;
    
    public PlayerListener(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(@NotNull PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Initialize player data
        PlayerData playerData = plugin.getDetectionManager().getPlayerData(player);
        
        // Record player in database
        plugin.getDatabaseManager().recordPlayer(
            player.getUniqueId().toString(),
            player.getName()
        );
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Initialized data for player: " + player.getName());
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(@NotNull PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Clean up player data
        plugin.getDetectionManager().removePlayerData(player.getUniqueId());
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Cleaned up data for player: " + player.getName());
        }
    }
}
