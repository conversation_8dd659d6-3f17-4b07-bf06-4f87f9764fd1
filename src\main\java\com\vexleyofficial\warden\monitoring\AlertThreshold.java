package com.vexleyofficial.warden.monitoring;

/**
 * Alert threshold configuration
 */
public class AlertThreshold {
    
    private final double value;
    private final long cooldownMs;
    
    public AlertThreshold(double value, long cooldownMs) {
        this.value = value;
        this.cooldownMs = cooldownMs;
    }
    
    public double getValue() {
        return value;
    }
    
    public long getCooldownMs() {
        return cooldownMs;
    }
}
