package com.vexleyofficial.warden.detection.checks;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.cloud.models.DetectionRequest;
import com.vexleyofficial.warden.cloud.models.DetectionResponse;
import com.vexleyofficial.warden.detection.data.PlayerData;
import com.vexleyofficial.warden.detection.data.ViolationData;
import com.vexleyofficial.warden.utils.VersionChecker;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Manages all anti-cheat checks and processes violations
 */
public class CheckManager {

    private final WardenAntiCheat plugin;
    private final CombatChecks combatChecks;
    private final MovementChecks movementChecks;
    private final WorldChecks worldChecks;

    public CheckManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.combatChecks = new CombatChecks(plugin);
        this.movementChecks = new MovementChecks(plugin);
        this.worldChecks = new WorldChecks(plugin);
    }

    /**
     * Initialize the check manager
     */
    public void initialize() {
        plugin.getWardenLogger().info("Check manager initialized with advanced detection modules");
    }
    
    /**
     * Process a check for a player
     */
    public void processCheck(@NotNull Player player, @NotNull PlayerData playerData, 
                           @NotNull String checkType, @NotNull Object... data) {
        
        // Create detection request for cloud analysis
        if (plugin.isCloudConnected()) {
            processCloudCheck(player, playerData, checkType, data);
        } else {
            // Fallback to local processing
            processLocalCheck(player, playerData, checkType, data);
        }
    }
    
    /**
     * Process check using cloud analysis
     */
    private void processCloudCheck(@NotNull Player player, @NotNull PlayerData playerData, 
                                 @NotNull String checkType, @NotNull Object... data) {
        
        try {
            // Build detection request
            DetectionRequest request = buildDetectionRequest(player, checkType, data);
            
            // Send to cloud for analysis
            CompletableFuture<DetectionResponse> future = plugin.getCloudCommunicator().sendDetectionRequest(request);
            
            future.thenAccept(response -> {
                if (response != null && response.isViolation()) {
                    handleViolation(player, playerData, checkType, response.getConfidence(), response);
                }
            }).exceptionally(throwable -> {
                plugin.getWardenLogger().warning("Cloud check failed for " + player.getName() + ": " + throwable.getMessage());
                // Fallback to local processing
                processLocalCheck(player, playerData, checkType, data);
                return null;
            });
            
        } catch (Exception e) {
            plugin.getWardenLogger().error("Error processing cloud check", e);
            processLocalCheck(player, playerData, checkType, data);
        }
    }
    
    /**
     * Process check using local analysis (fallback)
     */
    private void processLocalCheck(@NotNull Player player, @NotNull PlayerData playerData, 
                                 @NotNull String checkType, @NotNull Object... data) {
        
        // Simple local analysis based on check type
        double confidence = calculateLocalConfidence(checkType, data);
        
        if (confidence > 0.5) { // Threshold for local detection
            handleViolation(player, playerData, checkType, confidence, null);
        }
    }
    
    /**
     * Calculate confidence using local analysis
     */
    private double calculateLocalConfidence(@NotNull String checkType, @NotNull Object... data) {
        switch (checkType.toLowerCase()) {
            case "fly":
                if (data.length >= 2 && data[1] instanceof Double) {
                    double yDiff = (Double) data[1];
                    return Math.min(yDiff * 2, 1.0);
                }
                break;
                
            case "speed":
                if (data.length >= 3 && data[1] instanceof Double && data[2] instanceof Double) {
                    double actualSpeed = (Double) data[1];
                    double maxSpeed = (Double) data[2];
                    return Math.min((actualSpeed - maxSpeed) / maxSpeed, 1.0);
                }
                break;
                
            case "reach":
                if (data.length >= 3 && data[1] instanceof Double && data[2] instanceof Double) {
                    double actualReach = (Double) data[1];
                    double maxReach = (Double) data[2];
                    return Math.min((actualReach - maxReach) / maxReach, 1.0);
                }
                break;
                
            case "autoclicker":
                if (data.length >= 2 && data[1] instanceof Integer) {
                    int cps = (Integer) data[1];
                    return Math.min(cps / 30.0, 1.0);
                }
                break;
        }
        
        return 0.0;
    }
    
    /**
     * Handle a detected violation
     */
    private void handleViolation(@NotNull Player player, @NotNull PlayerData playerData, 
                               @NotNull String checkType, double confidence, DetectionResponse cloudResponse) {
        
        // Add violation to player data
        playerData.addViolation(checkType, confidence);
        ViolationData violationData = playerData.getViolationData(checkType);
        
        // Log violation
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug(String.format("Violation detected: %s by %s (confidence: %.2f)", 
                checkType, player.getName(), confidence));
        }
        
        // Record in database
        plugin.getDatabaseManager().recordViolation(
            player.getUniqueId().toString(),
            checkType,
            confidence,
            buildViolationData(checkType, confidence)
        );
        
        // Send alerts to staff
        sendStaffAlert(player, checkType, confidence);
        
        // Apply cloud mitigation if available
        if (cloudResponse != null) {
            plugin.getMitigationManager().applyMitigation(player, cloudResponse);
        }
        
        // Check if punishment is needed
        checkPunishment(player, violationData, confidence);
    }
    
    /**
     * Build detection request for cloud analysis
     */
    private DetectionRequest buildDetectionRequest(@NotNull Player player, @NotNull String checkType, @NotNull Object... data) {
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("checkType", checkType);
        requestData.put("data", data);
        
        // Build player context
        DetectionRequest.PlayerContext context = new DetectionRequest.PlayerContext(
            player.getLocation().getX(),
            player.getLocation().getY(),
            player.getLocation().getZ(),
            player.getLocation().getYaw(),
            player.getLocation().getPitch(),
            player.getVelocity().getX(),
            player.getVelocity().getY(),
            player.getVelocity().getZ(),
            player.getLocation().getBlock().getType().isSolid(),
            player.getLocation().getBlock().isLiquid(),
            player.getLocation().getBlock().getType().name().contains("LAVA"),
            player.getLocation().getBlock().getType().name().contains("WEB"),
            player.isFlying(),
            player.isSneaking(),
            player.isSprinting(),
            getPing(player),
            VersionChecker.getTPS(),
            player.getWorld().getName(),
            java.util.Arrays.asList(), // Nearby blocks would be calculated here
            new HashMap<>() // Player effects would be calculated here
        );
        
        return new DetectionRequest(
            player.getUniqueId().toString(),
            player.getName(),
            checkType,
            System.currentTimeMillis(),
            requestData,
            context
        );
    }
    
    /**
     * Send alert to staff members
     */
    private void sendStaffAlert(@NotNull Player player, @NotNull String checkType, double confidence) {
        if (!plugin.getConfigManager().isAlertsEnabled()) {
            return;
        }
        
        int confidencePercent = (int) Math.round(confidence * 100);
        
        if (confidencePercent < plugin.getConfigManager().getMinConfidence()) {
            return;
        }
        
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("player", player.getName());
        placeholders.put("cheat", checkType);
        placeholders.put("confidence", String.valueOf(confidencePercent));
        
        String alertMessage = plugin.getMessageManager().getMessage("cheat-detected", placeholders);
        
        // Send to online staff
        for (Player staff : Bukkit.getOnlinePlayers()) {
            if (staff.hasPermission("warden.alerts")) {
                staff.sendMessage(alertMessage);
            }
        }
        
        // Send to console
        plugin.getWardenLogger().info("ALERT: " + alertMessage);
    }
    
    /**
     * Check if punishment should be applied
     */
    private void checkPunishment(@NotNull Player player, @NotNull ViolationData violationData, double confidence) {
        if (!plugin.getConfigManager().isAutoPunish()) {
            return;
        }
        
        String action = null;
        
        if (violationData.isSevere() || confidence > 0.9) {
            action = plugin.getConfigManager().getSevereCheatAction();
        } else if (violationData.isModerate() || violationData.getViolationCount() > 1) {
            action = plugin.getConfigManager().getRepeatOffenseAction();
        } else if (violationData.getViolationCount() == 1) {
            action = plugin.getConfigManager().getFirstOffenseAction();
        }
        
        if (action != null) {
            applyPunishment(player, action, violationData.getCheckType());
        }
    }
    
    /**
     * Apply punishment to player
     */
    private void applyPunishment(@NotNull Player player, @NotNull String action, @NotNull String reason) {
        Bukkit.getScheduler().runTask(plugin, () -> {
            switch (action.toLowerCase()) {
                case "kick":
                    Map<String, String> kickPlaceholders = new HashMap<>();
                    kickPlaceholders.put("reason", reason);
                    String kickMessage = plugin.getMessageManager().getMessage(player, "kick-message", kickPlaceholders);
                    player.kickPlayer(kickMessage);
                    break;
                    
                case "ban":
                    Map<String, String> banPlaceholders = new HashMap<>();
                    banPlaceholders.put("reason", reason);
                    String banMessage = plugin.getMessageManager().getMessage(player, "ban-message", banPlaceholders);
                    Bukkit.getBanList(org.bukkit.BanList.Type.NAME).addBan(player.getName(), banMessage, null, null);
                    player.kickPlayer(banMessage);
                    break;
                    
                default:
                    if (action.startsWith("tempban-")) {
                        // Handle temporary bans (would need additional implementation)
                        plugin.getWardenLogger().info("Temporary ban not implemented: " + action);
                    }
                    break;
            }
        });
    }
    
    /**
     * Build violation data string for database storage
     */
    private String buildViolationData(@NotNull String checkType, double confidence) {
        Map<String, Object> data = new HashMap<>();
        data.put("checkType", checkType);
        data.put("confidence", confidence);
        data.put("timestamp", System.currentTimeMillis());
        
        return data.toString(); // In production, use proper JSON serialization
    }
    
    /**
     * Get player ping
     */
    private int getPing(@NotNull Player player) {
        try {
            // Use reflection for compatibility across versions
            return (Integer) player.getClass().getMethod("getPing").invoke(player);
        } catch (Exception e) {
            // Fallback for older versions or if method doesn't exist
            return 50; // Default ping
        }
    }
    
    /**
     * Reload check configuration
     */
    public void reload() {
        plugin.getWardenLogger().info("Check manager configuration reloaded");
    }

    // Getters for check modules
    public CombatChecks getCombatChecks() {
        return combatChecks;
    }

    public MovementChecks getMovementChecks() {
        return movementChecks;
    }

    public WorldChecks getWorldChecks() {
        return worldChecks;
    }
}
