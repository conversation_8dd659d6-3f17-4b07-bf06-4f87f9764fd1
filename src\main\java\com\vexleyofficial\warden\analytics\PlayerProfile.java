package com.vexleyofficial.warden.analytics;

import com.vexleyofficial.warden.detection.data.PlayerData;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Comprehensive player behavioral profile for advanced analytics
 */
public class PlayerProfile {
    
    private final UUID playerId;
    private final String playerName;
    private final long firstSeen;
    private final AtomicLong lastSeen;
    private final AtomicLong totalPlaytime;
    
    // Movement metrics
    private final Queue<Double> speedHistory;
    private final Queue<Double> jumpHistory;
    private final Queue<Double> directionHistory;
    private double averageSpeed;
    private double maxSpeed;
    private double movementConsistency;
    private int jumpFrequency;
    private int directionChanges;
    
    // Combat metrics
    private final Queue<Integer> cpsHistory;
    private final Queue<Double> reachHistory;
    private final Queue<Double> accuracyHistory;
    private double averageCPS;
    private int maxCPS;
    private double attackAccuracy;
    private double averageReach;
    private double combatConsistency;
    
    // Interaction metrics
    private final Queue<Double> breakTimeHistory;
    private final Queue<Boolean> oreFinds;
    private final Queue<Boolean> chestFinds;
    private double blockBreakEfficiency;
    private double oreFindRate;
    private double chestFindRate;
    
    // Behavioral analysis
    private double riskScore;
    private double consistencyScore;
    private double timingVariance;
    private final List<String> anomalies;
    private final Map<String, Long> anomalyTimestamps;
    
    // Activity tracking
    private final Queue<Long> sessionTimes;
    private boolean recentActivitySpike;
    
    public PlayerProfile(@NotNull UUID playerId, @NotNull String playerName) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.firstSeen = System.currentTimeMillis();
        this.lastSeen = new AtomicLong(System.currentTimeMillis());
        this.totalPlaytime = new AtomicLong(0);
        
        // Initialize collections with size limits
        this.speedHistory = new ConcurrentLinkedQueue<>();
        this.jumpHistory = new ConcurrentLinkedQueue<>();
        this.directionHistory = new ConcurrentLinkedQueue<>();
        this.cpsHistory = new ConcurrentLinkedQueue<>();
        this.reachHistory = new ConcurrentLinkedQueue<>();
        this.accuracyHistory = new ConcurrentLinkedQueue<>();
        this.breakTimeHistory = new ConcurrentLinkedQueue<>();
        this.oreFinds = new ConcurrentLinkedQueue<>();
        this.chestFinds = new ConcurrentLinkedQueue<>();
        this.sessionTimes = new ConcurrentLinkedQueue<>();
        this.anomalies = new ArrayList<>();
        this.anomalyTimestamps = new HashMap<>();
        
        // Initialize metrics
        this.riskScore = 0.0;
        this.consistencyScore = 0.0;
        this.timingVariance = 1.0;
        this.recentActivitySpike = false;
    }
    
    /**
     * Update movement-related metrics
     */
    public void updateMovementMetrics(@NotNull PlayerData playerData) {
        lastSeen.set(System.currentTimeMillis());
        
        // Update speed history
        double currentSpeed = playerData.getHorizontalSpeed();
        addToLimitedQueue(speedHistory, currentSpeed, 100);
        
        // Update jump tracking
        if (playerData.getCurrentLocation() != null && playerData.getLastLocation() != null) {
            double deltaY = playerData.getCurrentLocation().getY() - playerData.getLastLocation().getY();
            if (deltaY > 0.3) { // Likely a jump
                addToLimitedQueue(jumpHistory, deltaY, 50);
            }
        }
        
        // Track direction changes
        // This would require more sophisticated tracking in a full implementation
        addToLimitedQueue(directionHistory, Math.random(), 50); // Placeholder
    }
    
    /**
     * Update combat-related metrics
     */
    public void updateCombatMetrics(@NotNull PlayerData playerData) {
        // Update CPS history
        int currentCPS = playerData.getCps();
        addToLimitedQueue(cpsHistory, currentCPS, 50);
        
        // Update reach history
        double currentReach = playerData.getLastReach();
        if (currentReach > 0) {
            addToLimitedQueue(reachHistory, currentReach, 50);
        }
        
        // Update accuracy (placeholder - would need hit/miss tracking)
        addToLimitedQueue(accuracyHistory, 0.8 + Math.random() * 0.2, 50);
    }
    
    /**
     * Update interaction-related metrics
     */
    public void updateInteractionMetrics(@NotNull PlayerData playerData) {
        // Track block breaking efficiency
        // This would require tracking actual break times vs expected
        addToLimitedQueue(breakTimeHistory, Math.random(), 50);
        
        // Track ore and chest finding
        // These would be updated by specific events
        addToLimitedQueue(oreFinds, Math.random() > 0.9, 100);
        addToLimitedQueue(chestFinds, Math.random() > 0.95, 100);
    }
    
    /**
     * Update derived metrics from historical data
     */
    public void updateDerivedMetrics() {
        // Calculate movement metrics
        if (!speedHistory.isEmpty()) {
            averageSpeed = speedHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            maxSpeed = speedHistory.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            movementConsistency = calculateConsistency(speedHistory);
        }
        
        jumpFrequency = jumpHistory.size();
        directionChanges = directionHistory.size();
        
        // Calculate combat metrics
        if (!cpsHistory.isEmpty()) {
            averageCPS = cpsHistory.stream().mapToInt(Integer::intValue).average().orElse(0.0);
            maxCPS = cpsHistory.stream().mapToInt(Integer::intValue).max().orElse(0);
            combatConsistency = calculateConsistency(cpsHistory.stream().map(Double::valueOf).toList());
        }
        
        if (!reachHistory.isEmpty()) {
            averageReach = reachHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        }
        
        if (!accuracyHistory.isEmpty()) {
            attackAccuracy = accuracyHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        }
        
        // Calculate interaction metrics
        if (!breakTimeHistory.isEmpty()) {
            blockBreakEfficiency = breakTimeHistory.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        }
        
        oreFindRate = oreFinds.stream().mapToLong(b -> b ? 1 : 0).sum() / (double) Math.max(oreFinds.size(), 1);
        chestFindRate = chestFinds.stream().mapToLong(b -> b ? 1 : 0).sum() / (double) Math.max(chestFinds.size(), 1);
        
        // Calculate overall consistency score
        consistencyScore = (movementConsistency + combatConsistency) / 2.0;
        
        // Calculate timing variance
        timingVariance = calculateTimingVariance();
        
        // Check for activity spikes
        checkActivitySpike();
    }
    
    /**
     * Add anomaly with timestamp
     */
    public void addAnomalies(@NotNull List<String> newAnomalies) {
        long currentTime = System.currentTimeMillis();
        for (String anomaly : newAnomalies) {
            if (!anomalies.contains(anomaly)) {
                anomalies.add(anomaly);
            }
            anomalyTimestamps.put(anomaly, currentTime);
        }
        
        // Keep only recent anomalies in the main list
        anomalies.removeIf(anomaly -> {
            Long timestamp = anomalyTimestamps.get(anomaly);
            return timestamp != null && currentTime - timestamp > 3600000; // 1 hour
        });
    }
    
    /**
     * Get recent anomalies within time window
     */
    public List<String> getRecentAnomalies(long timeWindow) {
        long cutoffTime = System.currentTimeMillis() - timeWindow;
        return anomalies.stream()
            .filter(anomaly -> {
                Long timestamp = anomalyTimestamps.get(anomaly);
                return timestamp != null && timestamp > cutoffTime;
            })
            .toList();
    }
    
    /**
     * Calculate consistency score for a data series
     */
    private double calculateConsistency(@NotNull Collection<Double> data) {
        if (data.size() < 2) return 0.0;
        
        double mean = data.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = data.stream()
            .mapToDouble(value -> Math.pow(value - mean, 2))
            .average()
            .orElse(0.0);
        
        double standardDeviation = Math.sqrt(variance);
        
        // Consistency is inverse of coefficient of variation
        if (mean == 0) return 1.0;
        double coefficientOfVariation = standardDeviation / mean;
        return Math.max(0.0, 1.0 - coefficientOfVariation);
    }
    
    /**
     * Calculate timing variance for behavioral analysis
     */
    private double calculateTimingVariance() {
        if (sessionTimes.size() < 2) return 1.0;
        
        List<Long> intervals = new ArrayList<>();
        Long lastTime = null;
        
        for (Long time : sessionTimes) {
            if (lastTime != null) {
                intervals.add(time - lastTime);
            }
            lastTime = time;
        }
        
        if (intervals.isEmpty()) return 1.0;
        
        double mean = intervals.stream().mapToLong(Long::longValue).average().orElse(0.0);
        double variance = intervals.stream()
            .mapToDouble(interval -> Math.pow(interval - mean, 2))
            .average()
            .orElse(0.0);
        
        return Math.sqrt(variance) / mean; // Coefficient of variation
    }
    
    /**
     * Check for recent activity spikes
     */
    private void checkActivitySpike() {
        long currentTime = System.currentTimeMillis();
        long oneHourAgo = currentTime - 3600000;
        
        long recentSessions = sessionTimes.stream()
            .mapToLong(time -> time > oneHourAgo ? 1 : 0)
            .sum();
        
        recentActivitySpike = recentSessions > 10; // More than 10 sessions in an hour
    }
    
    /**
     * Add item to queue with size limit
     */
    private <T> void addToLimitedQueue(@NotNull Queue<T> queue, T item, int maxSize) {
        queue.offer(item);
        while (queue.size() > maxSize) {
            queue.poll();
        }
    }
    
    // Getters
    public UUID getPlayerId() { return playerId; }
    public String getPlayerName() { return playerName; }
    public long getFirstSeen() { return firstSeen; }
    public long getLastSeen() { return lastSeen.get(); }
    public long getTotalPlaytime() { return totalPlaytime.get(); }
    
    public double getAverageSpeed() { return averageSpeed; }
    public double getMaxSpeed() { return maxSpeed; }
    public double getMovementConsistency() { return movementConsistency; }
    public int getJumpFrequency() { return jumpFrequency; }
    public int getDirectionChanges() { return directionChanges; }
    
    public double getAverageCPS() { return averageCPS; }
    public int getMaxCPS() { return maxCPS; }
    public double getAttackAccuracy() { return attackAccuracy; }
    public double getAverageReach() { return averageReach; }
    public double getCombatConsistency() { return combatConsistency; }
    
    public double getBlockBreakEfficiency() { return blockBreakEfficiency; }
    public double getOreFindRate() { return oreFindRate; }
    public double getChestFindRate() { return chestFindRate; }
    
    public double getRiskScore() { return riskScore; }
    public void setRiskScore(double riskScore) { this.riskScore = riskScore; }
    
    public double getConsistencyScore() { return consistencyScore; }
    public double getTimingVariance() { return timingVariance; }
    public List<String> getAnomalies() { return new ArrayList<>(anomalies); }
    public boolean hasRecentActivitySpike() { return recentActivitySpike; }
}
