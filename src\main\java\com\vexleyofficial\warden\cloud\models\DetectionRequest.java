package com.vexleyofficial.warden.cloud.models;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

/**
 * Request model for cloud detection analysis
 */
public class DetectionRequest {
    
    private final String playerId;
    private final String playerName;
    private final String checkType;
    private final long timestamp;
    private final Map<String, Object> data;
    private final PlayerContext context;
    
    public DetectionRequest(@NotNull String playerId, 
                           @NotNull String playerName,
                           @NotNull String checkType,
                           long timestamp,
                           @NotNull Map<String, Object> data,
                           @NotNull PlayerContext context) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.checkType = checkType;
        this.timestamp = timestamp;
        this.data = data;
        this.context = context;
    }
    
    public String getPlayerId() {
        return playerId;
    }
    
    public String getPlayerName() {
        return playerName;
    }
    
    public String getCheckType() {
        return checkType;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public Map<String, Object> getData() {
        return data;
    }
    
    public PlayerContext getContext() {
        return context;
    }
    
    /**
     * Player context information for better detection accuracy
     */
    public static class PlayerContext {
        private final double x, y, z;
        private final float yaw, pitch;
        private final double velocityX, velocityY, velocityZ;
        private final boolean onGround;
        private final boolean inWater;
        private final boolean inLava;
        private final boolean inWeb;
        private final boolean flying;
        private final boolean sneaking;
        private final boolean sprinting;
        private final int ping;
        private final double tps;
        private final String world;
        private final List<String> nearbyBlocks;
        private final Map<String, Object> effects;
        
        public PlayerContext(double x, double y, double z,
                           float yaw, float pitch,
                           double velocityX, double velocityY, double velocityZ,
                           boolean onGround, boolean inWater, boolean inLava, boolean inWeb,
                           boolean flying, boolean sneaking, boolean sprinting,
                           int ping, double tps, String world,
                           List<String> nearbyBlocks, Map<String, Object> effects) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.yaw = yaw;
            this.pitch = pitch;
            this.velocityX = velocityX;
            this.velocityY = velocityY;
            this.velocityZ = velocityZ;
            this.onGround = onGround;
            this.inWater = inWater;
            this.inLava = inLava;
            this.inWeb = inWeb;
            this.flying = flying;
            this.sneaking = sneaking;
            this.sprinting = sprinting;
            this.ping = ping;
            this.tps = tps;
            this.world = world;
            this.nearbyBlocks = nearbyBlocks;
            this.effects = effects;
        }
        
        // Getters
        public double getX() { return x; }
        public double getY() { return y; }
        public double getZ() { return z; }
        public float getYaw() { return yaw; }
        public float getPitch() { return pitch; }
        public double getVelocityX() { return velocityX; }
        public double getVelocityY() { return velocityY; }
        public double getVelocityZ() { return velocityZ; }
        public boolean isOnGround() { return onGround; }
        public boolean isInWater() { return inWater; }
        public boolean isInLava() { return inLava; }
        public boolean isInWeb() { return inWeb; }
        public boolean isFlying() { return flying; }
        public boolean isSneaking() { return sneaking; }
        public boolean isSprinting() { return sprinting; }
        public int getPing() { return ping; }
        public double getTps() { return tps; }
        public String getWorld() { return world; }
        public List<String> getNearbyBlocks() { return nearbyBlocks; }
        public Map<String, Object> getEffects() { return effects; }
    }
}
