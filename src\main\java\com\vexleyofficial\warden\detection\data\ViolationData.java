package com.vexleyofficial.warden.detection.data;

import org.jetbrains.annotations.NotNull;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Stores violation data for a specific check type
 */
public class ViolationData {
    
    private final String checkType;
    private final AtomicInteger violationCount;
    private final AtomicLong lastViolationTime;
    private final AtomicLong firstViolationTime;
    private double highestConfidence;
    private double averageConfidence;
    private double totalConfidence;
    
    public ViolationData(@NotNull String checkType) {
        this.checkType = checkType;
        this.violationCount = new AtomicInteger(0);
        this.lastViolationTime = new AtomicLong(0);
        this.firstViolationTime = new AtomicLong(0);
        this.highestConfidence = 0.0;
        this.averageConfidence = 0.0;
        this.totalConfidence = 0.0;
    }
    
    /**
     * Add a new violation
     */
    public synchronized void addViolation(double confidence) {
        long currentTime = System.currentTimeMillis();
        int count = violationCount.incrementAndGet();
        
        lastViolationTime.set(currentTime);
        
        if (count == 1) {
            firstViolationTime.set(currentTime);
        }
        
        // Update confidence statistics
        if (confidence > highestConfidence) {
            highestConfidence = confidence;
        }
        
        totalConfidence += confidence;
        averageConfidence = totalConfidence / count;
    }
    
    /**
     * Reset violation data
     */
    public synchronized void reset() {
        violationCount.set(0);
        lastViolationTime.set(0);
        firstViolationTime.set(0);
        highestConfidence = 0.0;
        averageConfidence = 0.0;
        totalConfidence = 0.0;
    }
    
    /**
     * Check if violations have expired (no violations in the last X milliseconds)
     */
    public boolean hasExpired(long expirationTime) {
        long timeSinceLastViolation = System.currentTimeMillis() - lastViolationTime.get();
        return timeSinceLastViolation > expirationTime;
    }
    
    /**
     * Get violation rate (violations per minute)
     */
    public double getViolationRate() {
        if (violationCount.get() == 0 || firstViolationTime.get() == 0) {
            return 0.0;
        }
        
        long timeSpan = lastViolationTime.get() - firstViolationTime.get();
        if (timeSpan <= 0) {
            return 0.0;
        }
        
        double minutes = timeSpan / (1000.0 * 60.0);
        return violationCount.get() / minutes;
    }
    
    /**
     * Check if this violation data indicates a severe pattern
     */
    public boolean isSevere() {
        return violationCount.get() >= 10 && averageConfidence >= 0.8;
    }
    
    /**
     * Check if this violation data indicates a moderate pattern
     */
    public boolean isModerate() {
        return violationCount.get() >= 5 && averageConfidence >= 0.6;
    }
    
    // Getters
    public String getCheckType() {
        return checkType;
    }
    
    public int getViolationCount() {
        return violationCount.get();
    }
    
    public long getLastViolationTime() {
        return lastViolationTime.get();
    }
    
    public long getFirstViolationTime() {
        return firstViolationTime.get();
    }
    
    public double getHighestConfidence() {
        return highestConfidence;
    }
    
    public double getAverageConfidence() {
        return averageConfidence;
    }
    
    public double getTotalConfidence() {
        return totalConfidence;
    }
    
    /**
     * Get time since last violation in milliseconds
     */
    public long getTimeSinceLastViolation() {
        return System.currentTimeMillis() - lastViolationTime.get();
    }
    
    /**
     * Get total time span of violations in milliseconds
     */
    public long getViolationTimeSpan() {
        if (firstViolationTime.get() == 0) {
            return 0;
        }
        return lastViolationTime.get() - firstViolationTime.get();
    }
}
