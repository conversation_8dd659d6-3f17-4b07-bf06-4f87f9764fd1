package com.vexleyofficial.warden.detection.checks;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Advanced combat detection checks
 */
public class CombatChecks {
    
    private final WardenAntiCheat plugin;
    
    public CombatChecks(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Advanced reach detection with precise calculations
     */
    public void checkAdvancedReach(@NotNull Player attacker, @NotNull Entity target, @NotNull PlayerData playerData) {
        Location attackerLoc = attacker.getLocation();
        Location targetLoc = target.getLocation();
        
        // Calculate precise reach distance
        double reach = calculatePreciseReach(attackerLoc, targetLoc, attacker, target);
        
        // Base reach limit
        double maxReach = 3.0;
        
        // Adjust for lag compensation
        int ping = getPing(attacker);
        if (ping > 100) {
            maxReach += (ping - 100) * 0.001; // Small compensation for high ping
        }
        
        // Adjust for target movement
        if (target instanceof Player) {
            Player targetPlayer = (Player) target;
            double targetSpeed = targetPlayer.getVelocity().length();
            if (targetSpeed > 0.3) {
                maxReach += targetSpeed * 0.1; // Slight compensation for moving targets
            }
        }
        
        if (reach > maxReach) {
            double confidence = Math.min((reach - maxReach) / maxReach, 1.0);
            playerData.addViolation("reach", confidence);
            
            plugin.getDetectionManager().processCheck(attacker, "reach", confidence, reach, maxReach);
        }
    }
    
    /**
     * Kill aura detection with multiple algorithms
     */
    public void checkKillAura(@NotNull Player attacker, @NotNull Entity target, @NotNull PlayerData playerData) {
        // Check 1: Attack frequency
        checkAttackFrequency(attacker, playerData);
        
        // Check 2: Multi-target attacks
        checkMultiTargetAttacks(attacker, target, playerData);
        
        // Check 3: Impossible head rotation
        checkHeadRotation(attacker, target, playerData);
        
        // Check 4: Attack patterns
        checkAttackPatterns(attacker, playerData);
    }
    
    /**
     * Check for suspiciously high attack frequency
     */
    private void checkAttackFrequency(@NotNull Player attacker, @NotNull PlayerData playerData) {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastAttack = currentTime - playerData.getLastAttackTime();
        
        // Minecraft has a natural attack cooldown
        if (timeSinceLastAttack < 50) { // Less than 50ms is suspicious
            double confidence = 1.0 - (timeSinceLastAttack / 50.0);
            playerData.addViolation("killaura_frequency", confidence);
            
            plugin.getDetectionManager().processCheck(attacker, "killaura", confidence, timeSinceLastAttack);
        }
    }
    
    /**
     * Check for attacking multiple targets simultaneously
     */
    private void checkMultiTargetAttacks(@NotNull Player attacker, @NotNull Entity target, @NotNull PlayerData playerData) {
        // This would require tracking recent attack targets
        // For now, we'll implement a basic version
        
        List<Entity> nearbyEntities = attacker.getNearbyEntities(5, 5, 5);
        List<LivingEntity> potentialTargets = new ArrayList<>();
        
        for (Entity entity : nearbyEntities) {
            if (entity instanceof LivingEntity && entity != attacker) {
                potentialTargets.add((LivingEntity) entity);
            }
        }
        
        // If attacking while many targets are nearby, it might be kill aura
        if (potentialTargets.size() >= 3) {
            double confidence = Math.min(potentialTargets.size() / 5.0, 0.8);
            playerData.addViolation("killaura_multitarget", confidence);
            
            plugin.getDetectionManager().processCheck(attacker, "killaura", confidence, potentialTargets.size());
        }
    }
    
    /**
     * Check for impossible head rotation to target
     */
    private void checkHeadRotation(@NotNull Player attacker, @NotNull Entity target, @NotNull PlayerData playerData) {
        Location attackerLoc = attacker.getEyeLocation();
        Location targetLoc = target.getLocation().add(0, target.getHeight() / 2, 0);
        
        // Calculate required rotation to look at target
        Vector direction = targetLoc.toVector().subtract(attackerLoc.toVector()).normalize();
        float requiredYaw = (float) Math.toDegrees(Math.atan2(-direction.getX(), direction.getZ()));
        float requiredPitch = (float) Math.toDegrees(Math.asin(-direction.getY()));
        
        // Get current rotation
        float currentYaw = attackerLoc.getYaw();
        float currentPitch = attackerLoc.getPitch();
        
        // Calculate rotation difference
        float yawDiff = Math.abs(normalizeAngle(requiredYaw - currentYaw));
        float pitchDiff = Math.abs(requiredPitch - currentPitch);
        
        // If attacking without looking at target (kill aura indicator)
        if (yawDiff > 45 || pitchDiff > 30) {
            double confidence = Math.min((yawDiff + pitchDiff) / 90.0, 1.0);
            playerData.addViolation("killaura_rotation", confidence);
            
            plugin.getDetectionManager().processCheck(attacker, "killaura", confidence, yawDiff, pitchDiff);
        }
    }
    
    /**
     * Check for robotic attack patterns
     */
    private void checkAttackPatterns(@NotNull Player attacker, @NotNull PlayerData playerData) {
        // This would analyze timing patterns over multiple attacks
        // For now, we'll implement a basic consistency check
        
        long currentTime = System.currentTimeMillis();
        long timeSinceLastAttack = currentTime - playerData.getLastAttackTime();
        
        // Store attack intervals for pattern analysis
        // In a full implementation, you'd maintain a history of intervals
        
        // Check for perfectly consistent timing (bot-like)
        if (timeSinceLastAttack > 100 && timeSinceLastAttack < 200) {
            // Check if this timing is too consistent
            double consistency = calculateTimingConsistency(playerData);
            if (consistency > 0.9) {
                playerData.addViolation("killaura_pattern", consistency);
                
                plugin.getDetectionManager().processCheck(attacker, "killaura", consistency, consistency);
            }
        }
    }
    
    /**
     * Advanced auto-clicker detection
     */
    public void checkAutoClicker(@NotNull Player player, @NotNull PlayerData playerData) {
        // Check 1: CPS (Clicks Per Second)
        checkCPS(player, playerData);
        
        // Check 2: Click consistency
        checkClickConsistency(player, playerData);
        
        // Check 3: Humanness factor
        checkHumanness(player, playerData);
    }
    
    /**
     * Check clicks per second
     */
    private void checkCPS(@NotNull Player player, @NotNull PlayerData playerData) {
        int cps = playerData.getCps();
        
        // Human limit is around 15-20 CPS
        if (cps > 20) {
            double confidence = Math.min(cps / 30.0, 1.0);
            playerData.addViolation("autoclicker_cps", confidence);
            
            plugin.getDetectionManager().processCheck(player, "autoclicker", confidence, cps);
        }
    }
    
    /**
     * Check for perfectly consistent click timing
     */
    private void checkClickConsistency(@NotNull Player player, @NotNull PlayerData playerData) {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastClick = currentTime - playerData.getLastClickTime();
        
        // Perfectly consistent timing is suspicious
        if (timeSinceLastClick > 10 && timeSinceLastClick < 100) {
            double consistency = calculateClickConsistency(playerData);
            if (consistency > 0.95) {
                playerData.addViolation("autoclicker_consistency", consistency);
                
                plugin.getDetectionManager().processCheck(player, "autoclicker", consistency, consistency);
            }
        }
    }
    
    /**
     * Check for human-like clicking patterns
     */
    private void checkHumanness(@NotNull Player player, @NotNull PlayerData playerData) {
        // Humans have natural variations in clicking
        // This would analyze entropy in click patterns
        
        double entropy = calculateClickEntropy(playerData);
        if (entropy < 0.3) { // Low entropy = robotic
            double confidence = 1.0 - (entropy / 0.3);
            playerData.addViolation("autoclicker_humanness", confidence);
            
            plugin.getDetectionManager().processCheck(player, "autoclicker", confidence, entropy);
        }
    }
    
    /**
     * Critical hit detection
     */
    public void checkCriticals(@NotNull Player attacker, @NotNull Entity target, @NotNull PlayerData playerData) {
        // Check if player is falling (required for critical hits)
        boolean isFalling = attacker.getVelocity().getY() < 0;
        boolean isOnGround = attacker.isOnGround();
        
        // If player is on ground but dealing critical hits
        if (isOnGround && !isFalling) {
            // This would require checking if the damage dealt was critical
            // For now, we'll use a heuristic based on attack patterns
            
            double confidence = 0.7; // Base confidence for impossible critical
            playerData.addViolation("criticals", confidence);
            
            plugin.getDetectionManager().processCheck(attacker, "criticals", confidence, isOnGround, isFalling);
        }
    }
    
    // Helper methods
    
    private double calculatePreciseReach(@NotNull Location attackerLoc, @NotNull Location targetLoc, 
                                       @NotNull Player attacker, @NotNull Entity target) {
        // Calculate distance from attacker's eye to target's closest point
        Vector attackerEye = attackerLoc.toVector().add(new Vector(0, attacker.getEyeHeight(), 0));
        Vector targetCenter = targetLoc.toVector().add(new Vector(0, target.getHeight() / 2, 0));
        
        return attackerEye.distance(targetCenter);
    }
    
    private int getPing(@NotNull Player player) {
        try {
            return player.getPing();
        } catch (Exception e) {
            return 0;
        }
    }
    
    private float normalizeAngle(float angle) {
        while (angle > 180) angle -= 360;
        while (angle < -180) angle += 360;
        return angle;
    }
    
    private double calculateTimingConsistency(@NotNull PlayerData playerData) {
        // In a full implementation, this would analyze multiple attack intervals
        // For now, return a placeholder value
        return 0.5;
    }
    
    private double calculateClickConsistency(@NotNull PlayerData playerData) {
        // In a full implementation, this would analyze click timing patterns
        return 0.5;
    }
    
    private double calculateClickEntropy(@NotNull PlayerData playerData) {
        // In a full implementation, this would calculate entropy of click intervals
        return 0.5;
    }
}
