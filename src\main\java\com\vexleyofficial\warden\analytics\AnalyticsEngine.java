package com.vexleyofficial.warden.analytics;

import com.vexleyofficial.warden.WardenAntiCheat;
import com.vexleyofficial.warden.detection.data.PlayerData;
import com.vexleyofficial.warden.detection.data.ViolationData;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Advanced analytics engine for behavioral analysis and machine learning preparation
 */
public class AnalyticsEngine {
    
    private final WardenAntiCheat plugin;
    private final ScheduledExecutorService analyticsExecutor;
    private final Map<UUID, PlayerProfile> playerProfiles;
    private final Map<String, CheatStatistics> cheatStats;
    private final ServerMetrics serverMetrics;
    
    public AnalyticsEngine(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.analyticsExecutor = Executors.newScheduledThreadPool(2);
        this.playerProfiles = new ConcurrentHashMap<>();
        this.cheatStats = new ConcurrentHashMap<>();
        this.serverMetrics = new ServerMetrics();
    }
    
    /**
     * Initialize the analytics engine
     */
    public void initialize() {
        plugin.getWardenLogger().info("Initializing advanced analytics engine...");
        
        // Start periodic analytics tasks
        startPeriodicAnalysis();
        startMetricsCollection();
        startProfileUpdates();
        
        plugin.getWardenLogger().info("Analytics engine initialized successfully");
    }
    
    /**
     * Analyze player behavior and update profile
     */
    public void analyzePlayerBehavior(@NotNull Player player, @NotNull PlayerData playerData) {
        UUID playerId = player.getUniqueId();
        PlayerProfile profile = playerProfiles.computeIfAbsent(playerId, k -> new PlayerProfile(playerId, player.getName()));
        
        // Update behavioral metrics
        profile.updateMovementMetrics(playerData);
        profile.updateCombatMetrics(playerData);
        profile.updateInteractionMetrics(playerData);
        
        // Calculate risk score
        double riskScore = calculateRiskScore(profile, playerData);
        profile.setRiskScore(riskScore);
        
        // Check for anomalies
        List<String> anomalies = detectAnomalies(profile, playerData);
        if (!anomalies.isEmpty()) {
            profile.addAnomalies(anomalies);
            
            if (plugin.getConfigManager().isDebugMode()) {
                plugin.getWardenLogger().debug("Anomalies detected for " + player.getName() + ": " + anomalies);
            }
        }
        
        // Update cheat statistics
        updateCheatStatistics(playerData);
    }
    
    /**
     * Calculate player risk score based on behavior patterns
     */
    private double calculateRiskScore(@NotNull PlayerProfile profile, @NotNull PlayerData playerData) {
        double riskScore = 0.0;
        
        // Violation frequency factor
        int totalViolations = playerData.getViolations().values().stream()
            .mapToInt(ViolationData::getViolationCount)
            .sum();
        riskScore += Math.min(totalViolations * 0.1, 0.4); // Max 0.4 from violations
        
        // Violation confidence factor
        double avgConfidence = playerData.getViolations().values().stream()
            .mapToDouble(ViolationData::getAverageConfidence)
            .average()
            .orElse(0.0);
        riskScore += avgConfidence * 0.3; // Max 0.3 from confidence
        
        // Behavioral consistency factor
        double consistencyScore = profile.getConsistencyScore();
        if (consistencyScore > 0.9) { // Very consistent = suspicious
            riskScore += (consistencyScore - 0.9) * 3.0; // Max 0.3
        }
        
        // Time-based factor (new players are riskier)
        long accountAge = System.currentTimeMillis() - profile.getFirstSeen();
        if (accountAge < TimeUnit.HOURS.toMillis(24)) { // Less than 24 hours
            riskScore += 0.2;
        }
        
        // Recent activity spike
        if (profile.hasRecentActivitySpike()) {
            riskScore += 0.1;
        }
        
        return Math.min(riskScore, 1.0); // Cap at 1.0
    }
    
    /**
     * Detect behavioral anomalies
     */
    private List<String> detectAnomalies(@NotNull PlayerProfile profile, @NotNull PlayerData playerData) {
        List<String> anomalies = new ArrayList<>();
        
        // Movement anomalies
        if (profile.getAverageSpeed() > 6.0) {
            anomalies.add("EXCESSIVE_SPEED");
        }
        
        if (profile.getMovementConsistency() > 0.95) {
            anomalies.add("ROBOTIC_MOVEMENT");
        }
        
        // Combat anomalies
        if (profile.getAverageCPS() > 18) {
            anomalies.add("EXCESSIVE_CPS");
        }
        
        if (profile.getAttackAccuracy() > 0.98) {
            anomalies.add("PERFECT_ACCURACY");
        }
        
        // Interaction anomalies
        if (profile.getBlockBreakEfficiency() > 0.95) {
            anomalies.add("PERFECT_MINING");
        }
        
        // Pattern anomalies
        if (profile.getTimingVariance() < 0.05) {
            anomalies.add("CONSISTENT_TIMING");
        }
        
        // Violation pattern anomalies
        Map<String, ViolationData> violations = playerData.getViolations();
        if (violations.size() > 5) {
            long recentViolations = violations.values().stream()
                .mapToLong(v -> v.getTimeSinceLastViolation() < 60000 ? 1 : 0)
                .sum();
            
            if (recentViolations > 3) {
                anomalies.add("VIOLATION_SPIKE");
            }
        }
        
        return anomalies;
    }
    
    /**
     * Update global cheat statistics
     */
    private void updateCheatStatistics(@NotNull PlayerData playerData) {
        for (Map.Entry<String, ViolationData> entry : playerData.getViolations().entrySet()) {
            String cheatType = entry.getKey();
            ViolationData violationData = entry.getValue();
            
            CheatStatistics stats = cheatStats.computeIfAbsent(cheatType, k -> new CheatStatistics(cheatType));
            stats.addViolation(violationData.getAverageConfidence());
        }
    }
    
    /**
     * Generate machine learning training data
     */
    public Map<String, Object> generateMLTrainingData(@NotNull Player player, @NotNull PlayerData playerData) {
        Map<String, Object> trainingData = new HashMap<>();
        PlayerProfile profile = playerProfiles.get(player.getUniqueId());
        
        if (profile == null) return trainingData;
        
        // Player features
        trainingData.put("player_id", player.getUniqueId().toString());
        trainingData.put("account_age_hours", (System.currentTimeMillis() - profile.getFirstSeen()) / 3600000.0);
        trainingData.put("total_playtime_hours", profile.getTotalPlaytime() / 3600000.0);
        trainingData.put("risk_score", profile.getRiskScore());
        
        // Movement features
        trainingData.put("avg_speed", profile.getAverageSpeed());
        trainingData.put("max_speed", profile.getMaxSpeed());
        trainingData.put("movement_consistency", profile.getMovementConsistency());
        trainingData.put("jump_frequency", profile.getJumpFrequency());
        trainingData.put("direction_changes", profile.getDirectionChanges());
        
        // Combat features
        trainingData.put("avg_cps", profile.getAverageCPS());
        trainingData.put("max_cps", profile.getMaxCPS());
        trainingData.put("attack_accuracy", profile.getAttackAccuracy());
        trainingData.put("avg_reach", profile.getAverageReach());
        trainingData.put("combat_consistency", profile.getCombatConsistency());
        
        // Interaction features
        trainingData.put("block_break_efficiency", profile.getBlockBreakEfficiency());
        trainingData.put("ore_find_rate", profile.getOreFindRate());
        trainingData.put("chest_find_rate", profile.getChestFindRate());
        
        // Violation features
        Map<String, Integer> violationCounts = new HashMap<>();
        Map<String, Double> violationConfidences = new HashMap<>();
        
        for (Map.Entry<String, ViolationData> entry : playerData.getViolations().entrySet()) {
            violationCounts.put(entry.getKey() + "_count", entry.getValue().getViolationCount());
            violationConfidences.put(entry.getKey() + "_confidence", entry.getValue().getAverageConfidence());
        }
        
        trainingData.put("violation_counts", violationCounts);
        trainingData.put("violation_confidences", violationConfidences);
        
        // Temporal features
        trainingData.put("hour_of_day", Calendar.getInstance().get(Calendar.HOUR_OF_DAY));
        trainingData.put("day_of_week", Calendar.getInstance().get(Calendar.DAY_OF_WEEK));
        trainingData.put("server_tps", plugin.getServer().getTPS()[0]);
        trainingData.put("online_players", plugin.getServer().getOnlinePlayers().size());
        
        // Anomaly features
        trainingData.put("anomaly_count", profile.getAnomalies().size());
        trainingData.put("recent_anomalies", profile.getRecentAnomalies(TimeUnit.HOURS.toMillis(1)).size());
        
        return trainingData;
    }
    
    /**
     * Get server-wide analytics report
     */
    public AnalyticsReport generateAnalyticsReport() {
        AnalyticsReport report = new AnalyticsReport();
        
        // Player statistics
        report.setTotalPlayers(playerProfiles.size());
        report.setHighRiskPlayers(playerProfiles.values().stream()
            .mapToLong(p -> p.getRiskScore() > 0.7 ? 1 : 0)
            .sum());
        
        // Cheat statistics
        Map<String, Long> cheatCounts = cheatStats.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> e.getValue().getTotalViolations()
            ));
        report.setCheatStatistics(cheatCounts);
        
        // Top cheats by frequency
        List<String> topCheats = cheatStats.entrySet().stream()
            .sorted((a, b) -> Long.compare(b.getValue().getTotalViolations(), a.getValue().getTotalViolations()))
            .limit(5)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
        report.setTopCheats(topCheats);
        
        // Server metrics
        report.setServerMetrics(serverMetrics.getSnapshot());
        
        return report;
    }
    
    /**
     * Start periodic analysis tasks
     */
    private void startPeriodicAnalysis() {
        analyticsExecutor.scheduleAtFixedRate(() -> {
            try {
                // Cleanup old profiles
                cleanupOldProfiles();
                
                // Update global statistics
                updateGlobalStatistics();
                
                // Generate insights
                generateInsights();
                
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error in periodic analysis", e);
            }
        }, 5, 5, TimeUnit.MINUTES);
    }
    
    /**
     * Start metrics collection
     */
    private void startMetricsCollection() {
        analyticsExecutor.scheduleAtFixedRate(() -> {
            try {
                serverMetrics.update(plugin);
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error collecting metrics", e);
            }
        }, 0, 30, TimeUnit.SECONDS);
    }
    
    /**
     * Start profile updates
     */
    private void startProfileUpdates() {
        analyticsExecutor.scheduleAtFixedRate(() -> {
            try {
                for (PlayerProfile profile : playerProfiles.values()) {
                    profile.updateDerivedMetrics();
                }
            } catch (Exception e) {
                plugin.getWardenLogger().error("Error updating profiles", e);
            }
        }, 1, 1, TimeUnit.MINUTES);
    }
    
    /**
     * Cleanup old player profiles
     */
    private void cleanupOldProfiles() {
        long cutoffTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(7);
        
        playerProfiles.entrySet().removeIf(entry -> {
            PlayerProfile profile = entry.getValue();
            return profile.getLastSeen() < cutoffTime;
        });
    }
    
    /**
     * Update global statistics
     */
    private void updateGlobalStatistics() {
        // Update cheat trend analysis
        for (CheatStatistics stats : cheatStats.values()) {
            stats.updateTrends();
        }
    }
    
    /**
     * Generate behavioral insights
     */
    private void generateInsights() {
        // This would generate insights for administrators
        // Such as trending cheats, unusual patterns, etc.
        
        if (plugin.getConfigManager().isDebugMode()) {
            plugin.getWardenLogger().debug("Generated behavioral insights for " + playerProfiles.size() + " players");
        }
    }
    
    /**
     * Shutdown the analytics engine
     */
    public void shutdown() {
        analyticsExecutor.shutdown();
        try {
            if (!analyticsExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                analyticsExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            analyticsExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        plugin.getWardenLogger().info("Analytics engine shutdown complete");
    }
    
    // Getters
    public Map<UUID, PlayerProfile> getPlayerProfiles() {
        return new HashMap<>(playerProfiles);
    }
    
    public Map<String, CheatStatistics> getCheatStatistics() {
        return new HashMap<>(cheatStats);
    }
    
    public ServerMetrics getServerMetrics() {
        return serverMetrics;
    }
}
