package com.vexleyofficial.warden.monitoring;

import org.jetbrains.annotations.NotNull;

/**
 * Monitoring event for tracking alerts and incidents
 */
public class MonitoringEvent {
    
    private final String alertType;
    private final String message;
    private final AlertSeverity severity;
    private final long timestamp;
    private final String id;
    
    public MonitoringEvent(@NotNull String alertType, @NotNull String message, 
                          @NotNull AlertSeverity severity, long timestamp) {
        this.alertType = alertType;
        this.message = message;
        this.severity = severity;
        this.timestamp = timestamp;
        this.id = generateId();
    }
    
    private String generateId() {
        return alertType + "_" + timestamp + "_" + Math.abs(message.hashCode());
    }
    
    public String getAlertType() {
        return alertType;
    }
    
    public String getMessage() {
        return message;
    }
    
    public AlertSeverity getSeverity() {
        return severity;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public String getId() {
        return id;
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s: %s (at %d)", 
            severity, alertType, message, timestamp);
    }
}
