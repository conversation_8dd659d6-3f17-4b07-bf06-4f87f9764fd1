# Warden Anti-Cheat Configuration
# Version: 1.0.0-BETA
# Documentation: https://docs.warden.anticheat

# Cloud Configuration
cloud:
  # Your API key from https://panel.warden.anticheat
  api-key: "YOUR_API_KEY_HERE"
  
  # Cloud region: auto, us-east, eu-west, asia-pacific
  region: "auto"
  
  # Connection timeout in milliseconds
  timeout: 5000
  
  # Retry attempts for failed requests
  retry-attempts: 3
  
  # Enable cloud processing (disable for offline mode)
  enabled: true

# Detection Settings
detection:
  # Detection sensitivity: strict, balanced, lenient
  sensitivity: "balanced"
  
  # Enable real-time mitigation instead of just logging
  real-time-mitigation: true
  
  # Minimum TPS before disabling checks to prevent lag-related false positives
  min-tps: 18.0
  
  # Enable debug mode for detailed logging
  debug: false

# Punishment Configuration
punishments:
  # Actions: kick, tempban-1h, tempban-1d, tempban-7d, ban, warn
  first-offense: "kick"
  repeat-offense: "tempban-1h"
  severe-cheat: "ban"
  
  # Enable automatic punishment
  auto-punish: true
  
  # Broadcast punishments to staff
  broadcast-punishments: true

# Staff Notifications
notifications:
  # Enable alerts for staff members
  alerts-enabled: true
  
  # Alert format
  alert-format: "&c[Warden] &f{player} &7flagged for &c{cheat} &7(Confidence: {confidence}%)"
  
  # Minimum confidence level to send alerts (0-100)
  min-confidence: 85
  
  # Enable Discord webhook notifications
  discord-webhook: ""

# Player Whitelist
whitelist:
  # Players who bypass all checks
  players: []
  
  # Enable whitelist
  enabled: false

# Database Configuration
database:
  # Database type: sqlite, mysql, postgresql
  type: "sqlite"
  
  # SQLite file path (relative to plugin folder)
  sqlite-file: "warden.db"
  
  # MySQL/PostgreSQL settings
  host: "localhost"
  port: 3306
  database: "warden"
  username: "warden"
  password: "password"
  
  # Connection pool settings
  max-connections: 10
  connection-timeout: 30000

# Language Settings
language:
  # Language code: en, es, fr, de, ru, pt
  default: "en"
  
  # Enable automatic language detection based on client locale
  auto-detect: true

# Performance Settings
performance:
  # Maximum checks per player per second
  max-checks-per-second: 20
  
  # Enable async processing for heavy operations
  async-processing: true
  
  # Thread pool size for async operations
  thread-pool-size: 4

# Logging Configuration
logging:
  # Log level: debug, info, warn, error
  level: "info"
  
  # Enable file logging
  file-logging: true
  
  # Log file rotation (days)
  log-rotation-days: 7
  
  # Enable console logging
  console-logging: true
