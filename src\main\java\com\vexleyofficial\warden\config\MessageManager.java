package com.vexleyofficial.warden.config;

import com.vexleyofficial.warden.WardenAntiCheat;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages multi-language message system
 */
public class MessageManager {
    
    private final WardenAntiCheat plugin;
    private final Map<String, FileConfiguration> languageConfigs;
    private String defaultLanguage;
    
    public MessageManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.languageConfigs = new HashMap<>();
    }
    
    /**
     * Load messages from messages.yml
     */
    public void loadMessages() {
        defaultLanguage = plugin.getConfigManager().getDefaultLanguage();
        
        // Create messages.yml if it doesn't exist
        File messagesFile = new File(plugin.getDataFolder(), "messages.yml");
        if (!messagesFile.exists()) {
            plugin.saveResource("messages.yml", false);
        }
        
        // Load messages configuration
        FileConfiguration messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);
        
        // Load default configuration from jar
        InputStream defConfigStream = plugin.getResource("messages.yml");
        if (defConfigStream != null) {
            YamlConfiguration defConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defConfigStream));
            messagesConfig.setDefaults(defConfig);
        }
        
        // Cache all language configurations
        for (String language : messagesConfig.getKeys(false)) {
            if (messagesConfig.isConfigurationSection(language)) {
                languageConfigs.put(language, messagesConfig);
            }
        }
        
        plugin.getWardenLogger().info("Loaded messages for " + languageConfigs.size() + " languages");
    }
    
    /**
     * Get a message in the specified language
     */
    public String getMessage(@NotNull String language, @NotNull String key) {
        FileConfiguration config = languageConfigs.get(language);
        if (config == null) {
            config = languageConfigs.get(defaultLanguage);
        }
        
        if (config == null) {
            return "Message not found: " + key;
        }
        
        String message = config.getString(language + "." + key);
        if (message == null) {
            // Fallback to default language
            message = config.getString(defaultLanguage + "." + key);
        }
        
        if (message == null) {
            return "Message not found: " + key;
        }
        
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    /**
     * Get a message in the default language
     */
    public String getMessage(@NotNull String key) {
        return getMessage(defaultLanguage, key);
    }
    
    /**
     * Get a message for a specific player (auto-detect language if enabled)
     */
    public String getMessage(@NotNull Player player, @NotNull String key) {
        String language = defaultLanguage;
        
        if (plugin.getConfigManager().isAutoDetectLanguage()) {
            language = detectPlayerLanguage(player);
        }
        
        return getMessage(language, key);
    }
    
    /**
     * Get a message with placeholders replaced
     */
    public String getMessage(@NotNull String language, @NotNull String key, @NotNull Map<String, String> placeholders) {
        String message = getMessage(language, key);
        
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            message = message.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        
        return message;
    }
    
    /**
     * Get a message with placeholders replaced (default language)
     */
    public String getMessage(@NotNull String key, @NotNull Map<String, String> placeholders) {
        return getMessage(defaultLanguage, key, placeholders);
    }
    
    /**
     * Get a message with placeholders replaced for a specific player
     */
    public String getMessage(@NotNull Player player, @NotNull String key, @NotNull Map<String, String> placeholders) {
        String language = defaultLanguage;
        
        if (plugin.getConfigManager().isAutoDetectLanguage()) {
            language = detectPlayerLanguage(player);
        }
        
        return getMessage(language, key, placeholders);
    }
    
    /**
     * Send a message to a player
     */
    public void sendMessage(@NotNull Player player, @NotNull String key) {
        String message = getMessage(player, key);
        if (!message.trim().isEmpty()) {
            player.sendMessage(message);
        }
    }
    
    /**
     * Send a message to a player with placeholders
     */
    public void sendMessage(@NotNull Player player, @NotNull String key, @NotNull Map<String, String> placeholders) {
        String message = getMessage(player, key, placeholders);
        if (!message.trim().isEmpty()) {
            player.sendMessage(message);
        }
    }
    
    /**
     * Get prefixed message
     */
    public String getPrefixedMessage(@NotNull String language, @NotNull String key) {
        String prefix = getMessage(language, "prefix");
        String message = getMessage(language, key);
        return prefix + message;
    }
    
    /**
     * Get prefixed message (default language)
     */
    public String getPrefixedMessage(@NotNull String key) {
        return getPrefixedMessage(defaultLanguage, key);
    }
    
    /**
     * Get prefixed message for a player
     */
    public String getPrefixedMessage(@NotNull Player player, @NotNull String key) {
        String language = defaultLanguage;
        
        if (plugin.getConfigManager().isAutoDetectLanguage()) {
            language = detectPlayerLanguage(player);
        }
        
        return getPrefixedMessage(language, key);
    }
    
    /**
     * Send prefixed message to a player
     */
    public void sendPrefixedMessage(@NotNull Player player, @NotNull String key) {
        String message = getPrefixedMessage(player, key);
        if (!message.trim().isEmpty()) {
            player.sendMessage(message);
        }
    }
    
    /**
     * Send prefixed message to a player with placeholders
     */
    public void sendPrefixedMessage(@NotNull Player player, @NotNull String key, @NotNull Map<String, String> placeholders) {
        String language = defaultLanguage;
        
        if (plugin.getConfigManager().isAutoDetectLanguage()) {
            language = detectPlayerLanguage(player);
        }
        
        String prefix = getMessage(language, "prefix");
        String message = getMessage(language, key, placeholders);
        String fullMessage = prefix + message;
        
        if (!fullMessage.trim().isEmpty()) {
            player.sendMessage(fullMessage);
        }
    }
    
    /**
     * Detect player language based on client locale
     */
    private String detectPlayerLanguage(@NotNull Player player) {
        try {
            // Try to get player locale (requires modern server versions)
            String locale = player.getLocale();
            if (locale != null && locale.length() >= 2) {
                String language = locale.substring(0, 2).toLowerCase();
                if (languageConfigs.containsKey(language)) {
                    return language;
                }
            }
        } catch (Exception e) {
            // Fallback to default if locale detection fails
        }
        
        return defaultLanguage;
    }
    
    /**
     * Check if a language is supported
     */
    public boolean isLanguageSupported(@NotNull String language) {
        return languageConfigs.containsKey(language);
    }
    
    /**
     * Get all supported languages
     */
    public String[] getSupportedLanguages() {
        return languageConfigs.keySet().toArray(new String[0]);
    }
}
