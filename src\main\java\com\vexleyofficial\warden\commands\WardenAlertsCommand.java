package com.vexleyofficial.warden.commands;

import com.vexleyofficial.warden.WardenAntiCheat;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Command handler for /wardenalerts
 */
public class WardenAlertsCommand implements CommandExecutor {
    
    private final WardenAntiCheat plugin;
    private final Set<UUID> alertsDisabled;
    
    public WardenAlertsCommand(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.alertsDisabled = new HashSet<>();
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("This command can only be used by players.");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (!player.hasPermission("warden.alerts")) {
            plugin.getMessageManager().sendPrefixedMessage(player, "no-permission");
            return true;
        }
        
        UUID playerId = player.getUniqueId();
        
        if (alertsDisabled.contains(playerId)) {
            // Enable alerts
            alertsDisabled.remove(playerId);
            plugin.getMessageManager().sendPrefixedMessage(player, "alerts-enabled");
        } else {
            // Disable alerts
            alertsDisabled.add(playerId);
            plugin.getMessageManager().sendPrefixedMessage(player, "alerts-disabled");
        }
        
        return true;
    }
    
    /**
     * Check if a player has alerts enabled
     */
    public boolean hasAlertsEnabled(@NotNull Player player) {
        return !alertsDisabled.contains(player.getUniqueId());
    }
    
    /**
     * Check if a player has alerts enabled by UUID
     */
    public boolean hasAlertsEnabled(@NotNull UUID playerId) {
        return !alertsDisabled.contains(playerId);
    }
    
    /**
     * Enable alerts for a player
     */
    public void enableAlerts(@NotNull UUID playerId) {
        alertsDisabled.remove(playerId);
    }
    
    /**
     * Disable alerts for a player
     */
    public void disableAlerts(@NotNull UUID playerId) {
        alertsDisabled.add(playerId);
    }
}
