package com.vexleyofficial.warden.utils;

import com.vexleyofficial.warden.WardenAntiCheat;
import org.bukkit.Bukkit;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Level;

/**
 * Custom logger for Warden Anti-Cheat with file logging support
 */
public class Logger {
    
    private final WardenAntiCheat plugin;
    private final java.util.logging.Logger bukkitLogger;
    private final SimpleDateFormat dateFormat;
    private final SimpleDateFormat fileNameFormat;
    private File logFile;
    private PrintWriter fileWriter;
    
    public Logger(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
        this.bukkitLogger = plugin.getLogger();
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.fileNameFormat = new SimpleDateFormat("yyyy-MM-dd");
        
        initializeFileLogging();
    }
    
    private void initializeFileLogging() {
        if (plugin.getConfigManager() != null && plugin.getConfigManager().isFileLogging()) {
            try {
                File logsDir = new File(plugin.getDataFolder(), "logs");
                if (!logsDir.exists()) {
                    logsDir.mkdirs();
                }
                
                String fileName = "warden-" + fileNameFormat.format(new Date()) + ".log";
                logFile = new File(logsDir, fileName);
                
                fileWriter = new PrintWriter(new FileWriter(logFile, true));
                
                // Clean old log files
                cleanOldLogs(logsDir);
                
            } catch (IOException e) {
                bukkitLogger.warning("Failed to initialize file logging: " + e.getMessage());
            }
        }
    }
    
    private void cleanOldLogs(File logsDir) {
        if (plugin.getConfigManager() == null) return;
        
        int retentionDays = plugin.getConfigManager().getLogRotationDays();
        long cutoffTime = System.currentTimeMillis() - (retentionDays * 24L * 60L * 60L * 1000L);
        
        File[] logFiles = logsDir.listFiles((dir, name) -> name.startsWith("warden-") && name.endsWith(".log"));
        if (logFiles != null) {
            for (File file : logFiles) {
                if (file.lastModified() < cutoffTime) {
                    if (file.delete()) {
                        info("Deleted old log file: " + file.getName());
                    }
                }
            }
        }
    }
    
    public void info(@NotNull String message) {
        log(Level.INFO, message);
    }
    
    public void warning(@NotNull String message) {
        log(Level.WARNING, message);
    }
    
    public void severe(@NotNull String message) {
        log(Level.SEVERE, message);
    }
    
    public void debug(@NotNull String message) {
        if (plugin.getConfigManager() != null && plugin.getConfigManager().isDebugMode()) {
            log(Level.INFO, "[DEBUG] " + message);
        }
    }
    
    public void error(@NotNull String message, @NotNull Throwable throwable) {
        log(Level.SEVERE, message);
        if (fileWriter != null) {
            throwable.printStackTrace(fileWriter);
            fileWriter.flush();
        }
        throwable.printStackTrace();
    }
    
    private void log(@NotNull Level level, @NotNull String message) {
        // Console logging
        if (plugin.getConfigManager() == null || plugin.getConfigManager().isConsoleLogging()) {
            bukkitLogger.log(level, message);
        }
        
        // File logging
        if (fileWriter != null && (plugin.getConfigManager() == null || plugin.getConfigManager().isFileLogging())) {
            String timestamp = dateFormat.format(new Date());
            String logLevel = level.getName();
            String formattedMessage = String.format("[%s] [%s] %s", timestamp, logLevel, message);
            
            fileWriter.println(formattedMessage);
            fileWriter.flush();
        }
    }
    
    public void close() {
        if (fileWriter != null) {
            fileWriter.close();
        }
    }
}
