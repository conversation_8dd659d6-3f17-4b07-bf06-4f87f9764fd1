package com.vexleyofficial.warden.config;

import com.vexleyofficial.warden.WardenAntiCheat;
import org.bukkit.configuration.file.FileConfiguration;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * Manages plugin configuration settings
 */
public class ConfigManager {
    
    private final WardenAntiCheat plugin;
    private FileConfiguration config;
    
    // Cloud settings
    private String apiKey;
    private String cloudRegion;
    private int cloudTimeout;
    private int retryAttempts;
    private boolean cloudEnabled;
    
    // Detection settings
    private String detectionSensitivity;
    private boolean realTimeMitigation;
    private double minTPS;
    private boolean debugMode;
    
    // Punishment settings
    private String firstOffenseAction;
    private String repeatOffenseAction;
    private String severeCheatAction;
    private boolean autoPunish;
    private boolean broadcastPunishments;
    
    // Notification settings
    private boolean alertsEnabled;
    private String alertFormat;
    private int minConfidence;
    private String discordWebhook;
    
    // Whitelist settings
    private List<String> whitelistedPlayers;
    private boolean whitelistEnabled;
    
    // Database settings
    private String databaseType;
    private String sqliteFile;
    private String dbHost;
    private int dbPort;
    private String dbName;
    private String dbUsername;
    private String dbPassword;
    private int maxConnections;
    private int connectionTimeout;
    
    // Language settings
    private String defaultLanguage;
    private boolean autoDetectLanguage;
    
    // Performance settings
    private int maxChecksPerSecond;
    private boolean asyncProcessing;
    private int threadPoolSize;
    
    // Logging settings
    private String logLevel;
    private boolean fileLogging;
    private int logRotationDays;
    private boolean consoleLogging;
    
    public ConfigManager(@NotNull WardenAntiCheat plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Load configuration from config.yml
     */
    public void loadConfig() {
        config = plugin.getConfig();
        
        // Load cloud settings
        apiKey = config.getString("cloud.api-key", "YOUR_API_KEY_HERE");
        cloudRegion = config.getString("cloud.region", "auto");
        cloudTimeout = config.getInt("cloud.timeout", 5000);
        retryAttempts = config.getInt("cloud.retry-attempts", 3);
        cloudEnabled = config.getBoolean("cloud.enabled", true);
        
        // Load detection settings
        detectionSensitivity = config.getString("detection.sensitivity", "balanced");
        realTimeMitigation = config.getBoolean("detection.real-time-mitigation", true);
        minTPS = config.getDouble("detection.min-tps", 18.0);
        debugMode = config.getBoolean("detection.debug", false);
        
        // Load punishment settings
        firstOffenseAction = config.getString("punishments.first-offense", "kick");
        repeatOffenseAction = config.getString("punishments.repeat-offense", "tempban-1h");
        severeCheatAction = config.getString("punishments.severe-cheat", "ban");
        autoPunish = config.getBoolean("punishments.auto-punish", true);
        broadcastPunishments = config.getBoolean("punishments.broadcast-punishments", true);
        
        // Load notification settings
        alertsEnabled = config.getBoolean("notifications.alerts-enabled", true);
        alertFormat = config.getString("notifications.alert-format", 
            "&c[Warden] &f{player} &7flagged for &c{cheat} &7(Confidence: {confidence}%)");
        minConfidence = config.getInt("notifications.min-confidence", 85);
        discordWebhook = config.getString("notifications.discord-webhook", "");
        
        // Load whitelist settings
        whitelistedPlayers = config.getStringList("whitelist.players");
        whitelistEnabled = config.getBoolean("whitelist.enabled", false);
        
        // Load database settings
        databaseType = config.getString("database.type", "sqlite");
        sqliteFile = config.getString("database.sqlite-file", "warden.db");
        dbHost = config.getString("database.host", "localhost");
        dbPort = config.getInt("database.port", 3306);
        dbName = config.getString("database.database", "warden");
        dbUsername = config.getString("database.username", "warden");
        dbPassword = config.getString("database.password", "password");
        maxConnections = config.getInt("database.max-connections", 10);
        connectionTimeout = config.getInt("database.connection-timeout", 30000);
        
        // Load language settings
        defaultLanguage = config.getString("language.default", "en");
        autoDetectLanguage = config.getBoolean("language.auto-detect", true);
        
        // Load performance settings
        maxChecksPerSecond = config.getInt("performance.max-checks-per-second", 20);
        asyncProcessing = config.getBoolean("performance.async-processing", true);
        threadPoolSize = config.getInt("performance.thread-pool-size", 4);
        
        // Load logging settings
        logLevel = config.getString("logging.level", "info");
        fileLogging = config.getBoolean("logging.file-logging", true);
        logRotationDays = config.getInt("logging.log-rotation-days", 7);
        consoleLogging = config.getBoolean("logging.console-logging", true);
        
        plugin.getWardenLogger().info("Configuration loaded successfully");
    }
    
    // Cloud getters
    public String getApiKey() { return apiKey; }
    public String getCloudRegion() { return cloudRegion; }
    public int getCloudTimeout() { return cloudTimeout; }
    public int getRetryAttempts() { return retryAttempts; }
    public boolean isCloudEnabled() { return cloudEnabled; }
    
    // Detection getters
    public String getDetectionSensitivity() { return detectionSensitivity; }
    public boolean isRealTimeMitigation() { return realTimeMitigation; }
    public double getMinTPS() { return minTPS; }
    public boolean isDebugMode() { return debugMode; }
    
    // Punishment getters
    public String getFirstOffenseAction() { return firstOffenseAction; }
    public String getRepeatOffenseAction() { return repeatOffenseAction; }
    public String getSevereCheatAction() { return severeCheatAction; }
    public boolean isAutoPunish() { return autoPunish; }
    public boolean isBroadcastPunishments() { return broadcastPunishments; }
    
    // Notification getters
    public boolean isAlertsEnabled() { return alertsEnabled; }
    public String getAlertFormat() { return alertFormat; }
    public int getMinConfidence() { return minConfidence; }
    public String getDiscordWebhook() { return discordWebhook; }
    
    // Whitelist getters
    public List<String> getWhitelistedPlayers() { return whitelistedPlayers; }
    public boolean isWhitelistEnabled() { return whitelistEnabled; }
    
    // Database getters
    public String getDatabaseType() { return databaseType; }
    public String getSqliteFile() { return sqliteFile; }
    public String getDbHost() { return dbHost; }
    public int getDbPort() { return dbPort; }
    public String getDbName() { return dbName; }
    public String getDbUsername() { return dbUsername; }
    public String getDbPassword() { return dbPassword; }
    public int getMaxConnections() { return maxConnections; }
    public int getConnectionTimeout() { return connectionTimeout; }
    
    // Language getters
    public String getDefaultLanguage() { return defaultLanguage; }
    public boolean isAutoDetectLanguage() { return autoDetectLanguage; }
    
    // Performance getters
    public int getMaxChecksPerSecond() { return maxChecksPerSecond; }
    public boolean isAsyncProcessing() { return asyncProcessing; }
    public int getThreadPoolSize() { return threadPoolSize; }
    
    // Logging getters
    public String getLogLevel() { return logLevel; }
    public boolean isFileLogging() { return fileLogging; }
    public int getLogRotationDays() { return logRotationDays; }
    public boolean isConsoleLogging() { return consoleLogging; }
    
    /**
     * Check if API key is configured
     */
    public boolean isApiKeyConfigured() {
        return apiKey != null && !apiKey.equals("YOUR_API_KEY_HERE") && !apiKey.trim().isEmpty();
    }
    
    /**
     * Check if a player is whitelisted
     */
    public boolean isPlayerWhitelisted(String playerName) {
        return whitelistEnabled && whitelistedPlayers.contains(playerName.toLowerCase());
    }
}
