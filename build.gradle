plugins {
    id 'java'
    id 'maven-publish'
    id 'com.github.johnrengelman.shadow' version '8.1.1'
}

group = 'com.vexleyofficial'
version = '1.0.0-BETA'
description = 'Warden Anti-Cheat - Next-Generation Cloud-Powered Anti-Cheat Solution'

java {
    toolchain.languageVersion = JavaLanguageVersion.of(17)
}

repositories {
    mavenCentral()
    maven {
        name = 'spigotmc-repo'
        url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/'
    }
    maven {
        name = 'sonatype'
        url = 'https://oss.sonatype.org/content/groups/public/'
    }
    maven {
        name = 'papermc-repo'
        url = 'https://repo.papermc.io/repository/maven-public/'
    }
}

dependencies {
    // Spigot API for 1.8.8 compatibility
    compileOnly 'org.spigotmc:spigot-api:1.8.8-R0.1-SNAPSHOT'
    
    // Paper API for modern features (optional)
    compileOnly 'io.papermc.paper:paper-api:1.21.3-R0.1-SNAPSHOT'
    
    // HTTP client for cloud communication
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // JSON processing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Configuration handling
    implementation 'org.yaml:snakeyaml:2.2'
    
    // Logging
    implementation 'org.slf4j:slf4j-api:2.0.9'
    
    // Annotations
    compileOnly 'org.jetbrains:annotations:24.1.0'
    
    // Testing
    testImplementation 'org.junit.jupiter:junit-jupiter:5.10.1'
    testImplementation 'org.mockito:mockito-core:5.8.0'
}

processResources {
    def props = [version: version, description: description]
    inputs.properties props
    filteringCharset 'UTF-8'
    filesMatching('plugin.yml') {
        expand props
    }
}

shadowJar {
    archiveClassifier.set('')
    relocate 'okhttp3', 'com.vexleyofficial.warden.libs.okhttp3'
    relocate 'com.google.gson', 'com.vexleyofficial.warden.libs.gson'
    relocate 'org.yaml.snakeyaml', 'com.vexleyofficial.warden.libs.snakeyaml'
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}

test {
    useJUnitPlatform()
}
