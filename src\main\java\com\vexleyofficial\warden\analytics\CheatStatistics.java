package com.vexleyofficial.warden.analytics;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Statistics tracking for specific cheat types
 */
public class CheatStatistics {
    
    private final String cheatType;
    private final AtomicLong totalViolations;
    private final AtomicLong totalPlayers;
    private final List<Double> confidenceHistory;
    private final List<Long> timestampHistory;
    
    // Trend analysis
    private double averageConfidence;
    private double trendSlope;
    private long peakTime;
    private long peakViolations;
    
    public CheatStatistics(@NotNull String cheatType) {
        this.cheatType = cheatType;
        this.totalViolations = new AtomicLong(0);
        this.totalPlayers = new AtomicLong(0);
        this.confidenceHistory = new ArrayList<>();
        this.timestampHistory = new ArrayList<>();
        this.averageConfidence = 0.0;
        this.trendSlope = 0.0;
        this.peakTime = 0;
        this.peakViolations = 0;
    }
    
    /**
     * Add a new violation
     */
    public synchronized void addViolation(double confidence) {
        totalViolations.incrementAndGet();
        
        long currentTime = System.currentTimeMillis();
        confidenceHistory.add(confidence);
        timestampHistory.add(currentTime);
        
        // Keep only recent data (last 1000 violations)
        if (confidenceHistory.size() > 1000) {
            confidenceHistory.remove(0);
            timestampHistory.remove(0);
        }
        
        // Update average confidence
        averageConfidence = confidenceHistory.stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);
    }
    
    /**
     * Update trend analysis
     */
    public synchronized void updateTrends() {
        if (timestampHistory.size() < 10) return;
        
        // Calculate trend slope using linear regression
        trendSlope = calculateTrendSlope();
        
        // Find peak activity
        findPeakActivity();
    }
    
    /**
     * Calculate trend slope using simple linear regression
     */
    private double calculateTrendSlope() {
        int n = Math.min(timestampHistory.size(), 100); // Use last 100 data points
        if (n < 2) return 0.0;
        
        List<Long> recentTimestamps = timestampHistory.subList(timestampHistory.size() - n, timestampHistory.size());
        
        // Convert to relative time (hours from first timestamp)
        long baseTime = recentTimestamps.get(0);
        double[] x = new double[n];
        double[] y = new double[n];
        
        for (int i = 0; i < n; i++) {
            x[i] = (recentTimestamps.get(i) - baseTime) / 3600000.0; // Hours
            y[i] = i + 1; // Cumulative violations
        }
        
        // Calculate slope
        double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
        for (int i = 0; i < n; i++) {
            sumX += x[i];
            sumY += y[i];
            sumXY += x[i] * y[i];
            sumXX += x[i] * x[i];
        }
        
        double slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        return Double.isFinite(slope) ? slope : 0.0;
    }
    
    /**
     * Find peak activity period
     */
    private void findPeakActivity() {
        if (timestampHistory.size() < 10) return;
        
        long currentTime = System.currentTimeMillis();
        long oneHour = 3600000;
        
        long maxViolationsInHour = 0;
        long peakHourStart = 0;
        
        // Check each hour window
        for (int i = 0; i < timestampHistory.size(); i++) {
            long windowStart = timestampHistory.get(i);
            long windowEnd = windowStart + oneHour;
            
            long violationsInWindow = timestampHistory.stream()
                .mapToLong(time -> (time >= windowStart && time <= windowEnd) ? 1 : 0)
                .sum();
            
            if (violationsInWindow > maxViolationsInHour) {
                maxViolationsInHour = violationsInWindow;
                peakHourStart = windowStart;
            }
        }
        
        this.peakTime = peakHourStart;
        this.peakViolations = maxViolationsInHour;
    }
    
    /**
     * Get violations in the last hour
     */
    public long getRecentViolations() {
        long oneHourAgo = System.currentTimeMillis() - 3600000;
        return timestampHistory.stream()
            .mapToLong(time -> time > oneHourAgo ? 1 : 0)
            .sum();
    }
    
    /**
     * Get violations in the last day
     */
    public long getDailyViolations() {
        long oneDayAgo = System.currentTimeMillis() - 86400000;
        return timestampHistory.stream()
            .mapToLong(time -> time > oneDayAgo ? 1 : 0)
            .sum();
    }
    
    /**
     * Check if this cheat is trending upward
     */
    public boolean isTrendingUp() {
        return trendSlope > 0.1; // More than 0.1 violations per hour increase
    }
    
    /**
     * Check if this cheat is trending downward
     */
    public boolean isTrendingDown() {
        return trendSlope < -0.1; // More than 0.1 violations per hour decrease
    }
    
    /**
     * Get severity level based on recent activity
     */
    public String getSeverityLevel() {
        long recentViolations = getRecentViolations();
        
        if (recentViolations > 50) {
            return "CRITICAL";
        } else if (recentViolations > 20) {
            return "HIGH";
        } else if (recentViolations > 5) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }
    
    /**
     * Get confidence trend
     */
    public String getConfidenceTrend() {
        if (confidenceHistory.size() < 10) return "INSUFFICIENT_DATA";
        
        int recentSize = Math.min(10, confidenceHistory.size());
        double recentAvg = confidenceHistory.subList(confidenceHistory.size() - recentSize, confidenceHistory.size())
            .stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);
        
        if (recentAvg > averageConfidence + 0.1) {
            return "INCREASING";
        } else if (recentAvg < averageConfidence - 0.1) {
            return "DECREASING";
        } else {
            return "STABLE";
        }
    }
    
    // Getters
    public String getCheatType() {
        return cheatType;
    }
    
    public long getTotalViolations() {
        return totalViolations.get();
    }
    
    public long getTotalPlayers() {
        return totalPlayers.get();
    }
    
    public double getAverageConfidence() {
        return averageConfidence;
    }
    
    public double getTrendSlope() {
        return trendSlope;
    }
    
    public long getPeakTime() {
        return peakTime;
    }
    
    public long getPeakViolations() {
        return peakViolations;
    }
    
    public List<Double> getConfidenceHistory() {
        return new ArrayList<>(confidenceHistory);
    }
    
    public List<Long> getTimestampHistory() {
        return new ArrayList<>(timestampHistory);
    }
}
