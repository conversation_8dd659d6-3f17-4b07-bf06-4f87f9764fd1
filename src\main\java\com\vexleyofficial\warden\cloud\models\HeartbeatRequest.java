package com.vexleyofficial.warden.cloud.models;

import org.jetbrains.annotations.NotNull;

/**
 * Heartbeat request to maintain cloud connection
 */
public class HeartbeatRequest {
    
    private final int playerCount;
    private final long timestamp;
    private final String version;
    private final String status;
    
    public HeartbeatRequest(int playerCount, long timestamp, @NotNull String version) {
        this.playerCount = playerCount;
        this.timestamp = timestamp;
        this.version = version;
        this.status = "active";
    }
    
    public HeartbeatRequest(int playerCount, long timestamp, @NotNull String version, @NotNull String status) {
        this.playerCount = playerCount;
        this.timestamp = timestamp;
        this.version = version;
        this.status = status;
    }
    
    public int getPlayerCount() {
        return playerCount;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public String getVersion() {
        return version;
    }
    
    public String getStatus() {
        return status;
    }
}
